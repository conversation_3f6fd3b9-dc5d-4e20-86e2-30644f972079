<!-- 存证页面活动监控测试 -->
<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-800 to-gray-900 flex flex-col items-center justify-center p-8">
    <!-- 页面标题 -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-white mb-4">存证页面活动监控测试</h1>
      <p class="text-gray-300 text-lg">模拟存证页面的倒计时和活动监控功能</p>
    </div>

    <!-- 状态显示区域 -->
    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 mb-8 w-full max-w-2xl">
      <h2 class="text-2xl font-bold text-blue-300 mb-4 text-center">当前状态</h2>
      
      <!-- 监控状态和倒计时状态 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div class="bg-gray-700 bg-opacity-50 rounded-lg p-4 text-center">
          <div class="text-gray-300 mb-2">活动监控状态</div>
          <div class="flex items-center justify-center space-x-2">
            <div 
              :class="[
                'w-3 h-3 rounded-full transition-all duration-300',
                monitorStatus ? 'bg-green-400 animate-pulse' : 'bg-red-400'
              ]"
            ></div>
            <span class="text-white font-bold">
              {{ monitorStatus ? '运行中' : '已停止' }}
            </span>
          </div>
        </div>
        
        <div class="bg-gray-700 bg-opacity-50 rounded-lg p-4 text-center">
          <div class="text-gray-300 mb-2">倒计时状态</div>
          <div class="text-white font-bold">
            {{ countdown > 0 ? '进行中' : '未开始' }}
          </div>
        </div>
      </div>

      <!-- 倒计时显示 -->
      <div v-if="countdown > 0" class="text-center mb-4">
        <div class="relative w-32 h-32 mx-auto">
          <svg class="w-full h-full" viewBox="0 0 100 100">
            <!-- 背景圆环 -->
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="rgba(255,255,255,0.1)"
              stroke-width="4"
            />
            <!-- 进度条圆环 -->
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="url(#progressGradient)"
              stroke-width="4"
              stroke-linecap="round"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="dashOffset"
              transform="rotate(-90 50 50)"
            />
            <!-- 渐变定义 -->
            <defs>
              <linearGradient
                id="progressGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stop-color="#3B82F6" />
                <stop offset="100%" stop-color="#10B981" />
              </linearGradient>
            </defs>
          </svg>
          
          <!-- 倒计时数字 -->
          <div class="absolute inset-0 flex flex-col items-center justify-center">
            <div class="text-2xl font-mono font-bold text-white">
              {{ countdown }}
            </div>
            <div class="text-gray-300 text-xs">秒</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 控制按钮区域 -->
    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 mb-8 w-full max-w-2xl">
      <h2 class="text-2xl font-bold text-blue-300 mb-4 text-center">测试控制</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- 开始倒计时按钮（模拟存证开始） -->
        <button
          @click="startCountdown"
          :disabled="countdown > 0"
          class="flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          <i class="fas fa-play text-lg"></i>
          <span class="font-medium">开始存证倒计时</span>
        </button>

        <!-- 停止倒计时按钮 -->
        <button
          @click="stopCountdown"
          :disabled="countdown === 0"
          class="flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          <i class="fas fa-stop text-lg"></i>
          <span class="font-medium">停止倒计时</span>
        </button>

        <!-- 手动启动监控按钮 -->
        <button
          @click="startMonitoring"
          :disabled="monitorStatus"
          class="flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          <i class="fas fa-play-circle text-lg"></i>
          <span class="font-medium">启动活动监控</span>
        </button>

        <!-- 手动停止监控按钮 -->
        <button
          @click="stopMonitoring"
          :disabled="!monitorStatus"
          class="flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          <i class="fas fa-stop-circle text-lg"></i>
          <span class="font-medium">停止活动监控</span>
        </button>
      </div>
    </div>

    <!-- 日志显示区域 -->
    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 w-full max-w-2xl mb-8">
      <h2 class="text-2xl font-bold text-blue-300 mb-4 text-center">操作日志</h2>
      
      <div class="bg-black bg-opacity-30 rounded-lg p-4 h-48 overflow-y-auto">
        <div v-for="(log, index) in logs" :key="index" class="text-sm mb-2">
          <span class="text-gray-400">{{ log.time }}</span>
          <span :class="log.type === 'warning' ? 'text-yellow-300' : log.type === 'error' ? 'text-red-300' : 'text-white'" class="ml-2">
            {{ log.message }}
          </span>
        </div>
        <div v-if="logs.length === 0" class="text-gray-400 text-center">
          暂无日志记录
        </div>
      </div>
      
      <button
        @click="clearLogs"
        class="mt-4 w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
      >
        清空日志
      </button>
    </div>

    <!-- 返回按钮 -->
    <div class="flex gap-4">
      <button
        @click="goBack"
        class="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105"
      >
        <i class="fas fa-arrow-left text-lg"></i>
        <span class="font-medium">返回首页</span>
      </button>
      
      <button
        @click="goToInactivityTest"
        class="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105"
      >
        <i class="fas fa-flask text-lg"></i>
        <span class="font-medium">活动监控测试</span>
      </button>
    </div>
  </div>
</template>

<script>
import inactivityMonitor from "@/utils/inactivityMonitor";

export default {
  name: "SaveTest",
  data() {
    return {
      countdown: 0,
      countdownConfig: 15, // 15秒用于测试
      timer: null,
      circumference: 2 * Math.PI * 40,
      monitorStatus: false,
      logs: []
    };
  },
  
  computed: {
    dashOffset() {
      const progress = this.countdown / this.countdownConfig;
      return this.circumference * (1 - progress);
    }
  },
  
  mounted() {
    this.addLog("存证测试页面加载完成");
    this.updateMonitorStatus();
  },
  
  beforeDestroy() {
    // 清理倒计时定时器
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    this.addLog("页面即将销毁，清理资源");
  },
  
  methods: {
    // 开始倒计时（模拟存证页面的 startCountdown 方法）
    startCountdown() {
      this.addLog("🔄 开始存证倒计时，停止活动监控", "warning");
      
      // 停止活动监控（模拟存证页面的行为）
      inactivityMonitor.stop();
      this.updateMonitorStatus();
      
      // 开始倒计时
      this.countdown = this.countdownConfig;
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(this.timer);
          this.timer = null;
          this.handleCountdownEnd();
        }
      }, 1000);
    },
    
    // 停止倒计时
    stopCountdown() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        this.countdown = 0;
        this.addLog("⏹️ 手动停止倒计时");
      }
    },
    
    // 倒计时结束处理（模拟存证页面的 handleCountdownEnd 方法）
    handleCountdownEnd() {
      this.addLog("⏰ 倒计时结束，重新启动活动监控", "warning");
      this.countdown = 0;
      
      // 重新启动活动监控（模拟存证页面的 restartInactivityMonitor 方法）
      this.restartInactivityMonitor();
    },
    
    // 重新启动活动监控（模拟存证页面的方法）
    restartInactivityMonitor() {
      this.addLog("🔄 重新启动活动监控");
      
      // 使用默认配置启动活动监控
      inactivityMonitor.start(
        () => {
          this.addLog("⚠️ 检测到无活动，触发超时回调", "error");
          alert("检测到长时间无活动，将跳转到首页");
          this.$router.push("/");
        },
        30000, // 30秒用于测试
        (remainingSeconds) => {
          this.addLog(`⚠️ 警告：还有 ${remainingSeconds} 秒将触发超时`, "warning");
        },
        10000 // 10秒警告时间
      );
      this.updateMonitorStatus();
    },
    
    // 手动启动监控
    startMonitoring() {
      this.addLog("▶️ 手动启动活动监控");
      this.restartInactivityMonitor();
    },
    
    // 手动停止监控
    stopMonitoring() {
      this.addLog("⏹️ 手动停止活动监控");
      inactivityMonitor.stop();
      this.updateMonitorStatus();
    },
    
    // 更新监控状态
    updateMonitorStatus() {
      this.monitorStatus = inactivityMonitor.isEnabled;
    },
    
    // 添加日志
    addLog(message, type = "info") {
      const now = new Date();
      const time = now.toLocaleTimeString();
      this.logs.unshift({
        time,
        message,
        type
      });
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50);
      }
    },
    
    // 清空日志
    clearLogs() {
      this.logs = [];
      this.addLog("📝 日志已清空");
    },
    
    // 返回首页
    goBack() {
      this.addLog("🏠 返回首页");
      this.$router.push("/");
    },
    
    // 跳转到活动监控测试页面
    goToInactivityTest() {
      this.addLog("🧪 跳转到活动监控测试页面");
      this.$router.push("/test/inactivity");
    }
  }
};
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}
</style>
