# 前端交互协议

### 请求获取服务状态

#### 请求获取服务状态 - 10033

|字段|类型|说明|
| ------| ------| ------|

#### 获取服务端状态响应 - 20033

|字段|类型|说明|
||||
| ------| ------| ------|

### 请求获取告警倒计时

#### 请求获取告警倒计时 - 10301

|字段|类型|说明|
| ------| ------| ------|

#### 获取服务端状态响应 - 20301

|字段|类型|说明|
|timeoutAlarm|int|超时时间，单位秒|
| ------| ------| ------|

## 生物信息

### 获取登录方式

获取登录方式

#### 获取登录方式-10010

#### 获取登录方式响应-20010

|字段|类型|说明|
| ------| --------| ----------------------------------------------------------------------------------------------|
|type|string|认证方式- FINGERPRINT<br />- FACE<br />- FACE\_AND\_FINGERPRINT<br />- FACE\_OR\_FINGERPRINT|

#### 通知登录方式-30110

在策略更改时发送通知

|字段|类型|说明|
| ------| --------| ----------------------------------------------------------------------------------------------|
|type|string|认证方式- FINGERPRINT<br />- FACE<br />- FACE\_AND\_FINGERPRINT<br />- FACE\_OR\_FINGERPRINT|

### 请求开始静脉识别

#### 请求开始静脉识别请求-10001

#### 请求开始静脉识别响应-20001

### 静脉识别结果通知 - 30001

静脉识别成功后的通知，如果识别失败会发送错误信息

type: 30001

|字段|类型|说明|
| ----------| --------| ----------------------------------|
|account|string|账户名称|
|roleType|int|角色类型 1超管 2管理员 3普通用户|
||||

### 请求人脸识别

#### 请求人脸识别请求-10002

#### 请求人脸识别响应-20002

|字段|类型|说明|
| --------| --------| ----------------|
|devide|string|摄像头设备名称|

### 人脸识别结果通知 - 30002

人脸识别成功后的通知，如果识别失败会发送错误信息

type: 30002

|字段|类型|说明|
| ----------| --------| ----------------------------------|
|account|string|用户名|
|roleType|int|角色类型 1超管 2管理员 3普通用户|

### 人脸图像通知 - 30102

人脸图像

|字段|类型|说明|
| ------| --------| ------------|
|img|string|图像base64|

### 请求静脉采集

#### 请求静脉采集请求-10016

|字段|类型|说明|
|account|string|用户|
| ------| ------| ------|

#### 请求静脉采集响应-20016

### 通知静脉采集序号 - 30161

|字段|类型|说明|
|index|||
| ------| ------| ------|

### 通知静脉移除手指 - 30162

### 通知静脉特征 - 30163

|字段|类型|说明|
|​`fingerChara`​|string|静脉模板|
| ------| ------| ------|

### 通知静脉采集异常 - 30164

## 请求静脉对比 - 10018

用于采集完成后的对比

### 请求静脉对比请求-10018

|字段|类型|说明|
|fingerChara|string|静脉特征|
| ------| ------| ------|

### 请求静脉对比响应 - 20018

|字段|类型|说明|
|​`fingerChara`​|string|静脉模板|
| ------| ------| ------|

### 请求人脸采集

#### 请求人脸采集请求-10017

|字段|类型|说明|
|account|string|用户|
| ------| ------| ------|

#### 请求人脸采集响应-20017

### 请求停止静脉识别

#### 请求停止静脉识别-10021

|字段|类型|说明|
||||
| ------| ------| ------|

#### 请求停止静脉识别响应-20021

### 请求停止人脸识别

#### 请求停止人脸识别-10023

#### 请求停止人脸识别响应-20023

## 请求保存生物特征- 10019

用于采集完成后的对比

### 请求保存生物特征请求-10019

|字段|类型|说明|
|userId|int|用户ID|
| ---------| --------| ---------------------|
|type|int|类型：1-静脉 2-人脸|
|content|string|生物信息数据|

### 保存生物特征响应 - 20019

## 获取用户生物特征- 10020

### 请求获取生物特征请求-10020

|字段|类型|说明|
|userId|int|用户ID|
| ------| ------| ---------------------|
|type|int|类型：1-静脉 2-人脸|

### 保存生物特征响应 - 20020

|字段|类型|说明|
| ---------| --------| ---------------------|
|userId|int|用户ID|
|type|int|类型：1-静脉 2-人脸|
|content|string|生物信息数据|

## 柜子管理

### 请求获取所有柜子及状态

#### 请求获取所有柜子状态-10003

#### 获取所有柜子状态响应-20003

|字段|类型|说明|
| -------| --------------| ---------------|
|grids|object array|grid 状态数组|

### 查询柜门状态

#### 查询柜格状态-10006

|字段|类型|说明|
| ----------| ------| ----------|
|gridCode|int|柜格编号|

#### 查询柜格响应-20006

type: 20006

|字段|类型|说明|
| ------| --------| ----------|
|grid|object|柜格对象|

### 通知柜门关闭 - 30006

柜门关闭后会通知当前柜子关闭

type: 30006

|字段|类型|说明|
| -----------| --------| --------------------------|
|gridCode|int|柜格编号|
|hasCard|int|- 0：有卡片<br />- 1：有卡片|
|depositld|string|卡片ID|

### 通知柜门全部关闭 - 30007

当柜门全部关闭后会通知当前柜子关闭

type: 30007

|字段|类型|说明|
| ---------| ------| ----------------------------------------------------|
|success|int|- 1: 成功，班组模式只要满足一个即为成功<br />- 0：失败|

‍

## 存取证流程

### 请求判断现在柜格是否满

#### 请求判断现在柜格是否满-10004

#### 请求判断现在柜格是否满响应-20004

|字段|类型|说明|
| ------------| ------| --------------------------------------|
|gridIsFull|int|现在柜格是否满了- 0：未满<br />- 1：已满|

### 请求分配柜格（存证）

分配成功柜门会开启

#### 请求分配柜格-10005

|字段|类型|说明|
| ----------| --------| ------------------------------------|
|gridCode|int|可选，柜格编号，当没传时会自动分配|
|username|string|用户名|

#### 分配柜格响应-20005

|字段|类型|说明|
| ------| -------| ---------------------------------|
|list|array|分配的柜格编号， 这个柜格会打开|

|字段|类型|说明|
| -----------| --------| ----------|
|gridCode|int|柜格编号|
|account|string|用户名|
|depositld|string|证件信息|

### 通知存证状态 - 30005

存证请求时，当用户关闭全部柜门后会通知存证状态

type: 30005

成功时，code为1，失败时code不为1

|字段|类型|说明|
| ------| -------| -----------------------------|
|list|array|分配的柜格， 这个柜格会打开|

|字段|类型|说明|
| -----------| --------| ----------|
|gridCode|int|柜格编号|
|account|string|用户名|
|depositld|string|证件信息|

### 通知存证异常 - 30008

存证时，证件不匹配，自动开柜

|字段|类型|说明|
| ----------| ------| --------|
|gridCode|int|柜格号|

### 通知柜门关闭 - 30006

柜门关闭后会通知当前柜子关闭

type: 30006

|字段|类型|说明|
| -----------| --------| --------------------------|
|gridCode|int|柜格编号|
|hasCard|int|- 0：有卡片<br />- 1：有卡片|
|depositld|string|卡片ID|

### 通知柜门全部关闭 - 30007

当柜门全部关闭后会通知当前柜子关闭

type: 30007

### 开启指定柜门（存证）

#### 存证再次开启柜门-10007

|字段|类型|说明|
| ----------| --------| ----------|
|gridCode|int|柜格编号|
|username|string|用户名|

#### 存证再次开柜响应-20007

|字段|类型|说明|
| ----------| ------| ----------|
|gridCode|int|柜格编号|

### 查询用户是否取证

#### 查询用户是否可以取证-10008

|字段|类型|说明|
| ---------| --------| --------|
|account|string|用户名|

#### 查询用户是否可以取证-20008

|字段|类型|说明|
| ---------| -----------| --------------|
|account|string|用户名|
|list|int array|柜格编号数组|

|字段|类型|说明|
| -----------| --------| --------------|
|gridCode|int|柜格编号|
|account|string|用户名|
|depositld|string|取的证件信息|

### 取证

请求成功会自动开启柜门

#### 取证-10009

|字段|类型|说明|
| ---------| --------| ----------|
|account|string|账户名称|

#### 取证响应-20009

|字段|类型|说明|
| ---------| --------| --------------|
|account|string|柜格编号数组|
|list|array|证件柜信息|

|字段|类型|说明|
| -----------| --------| --------------|
|gridCode|int|柜格编号|
|account|string|用户名|
|depositld|string|取的证件信息|

#### 通知取证状态 - 30009

取证请求时，当用户关闭全部柜门后会通知取证状态

type: 30009

成功时，code为1，失败时code不为1

|字段|类型|说明|
| ------| -------| -----------------------------|
|list|array|分配的柜格， 这个柜格会打开|

|字段|类型|说明|
| -----------| --------| ------------------------------|
|gridCode|int|柜格编号|
|account|string|用户名|
|depositld|string|取的证件信息|
|hasCard|int|0：没有证存放<br />- 1：可以取证|

#### 通知取证状态 - 30010

当用户取证时，没取证件就关门，自动开柜

|字段|类型|说明|
| ----------| ------| ----------|
|gridCode|int|柜格编号|

### 验证授权码

#### 取证-10011

|字段|类型|说明|
| ----------| --------| ------------|
|authCode|string|授权码|
|code|string|授权码明文|

#### 取证响应-20011

## 配置管理员

### 请求登录配置用户

#### 请求登录配置用户 - 10030

|字段|类型|说明|
|username|string|用户名：默认为icbAdmin|
|password|string|密码:默认为icbAdmin|
| ------| ------| ------|

#### 获取登录配置用户 - 20030

### 请求获取服务端配置项

#### 请求获取服务端配置项 - 10031

#### 获取服务端配置项响应 - 20031

|字段|类型|说明|
|serverIp|string|服务端IP|
|serverPort|int|服务端端口|
|offline|int|是否是离线模式- 0: 离线模式<br />- 1:在线模式|
| -------------| --------| ------------|
|cameraIndex|int|摄像头序号|
|cameraName|string|摄像头名称|

### 请求设置服务端配置项

#### 请求设置服务端配置项 - 10032

|字段|类型|说明|
|serverIp|string|服务端IP|
|serverPort|int|服务端端口|
|offline|int|是否是离线模式- 0: 离线模式<br />- 1:在线模式|
| ------| ------| ------|

#### 设置服务端配置项响应 - 20032

### 管理员开指定柜格 - 10034

#### 管理员开指定柜格请求 - 10034

|字段|类型|说明|
| ----------| ------| ----------|
|gridCode|int|柜格编号|

#### 管理员开指定柜格响应-20034

|字段|类型|说明|
| ----------| ------| ----------|
|gridCode|int|柜格编号|

### 管理员开全部柜格- 10035

#### 管理员开全部柜格请求 - 10035

#### 管理员开全部柜格响应-20035

### 通知柜门关闭 - 30006

柜门关闭后会通知当前柜子关闭

type: 30006

|字段|类型|说明|
| -----------| --------| --------------------------|
|gridCode|int|柜格编号|
|hasCard|int|- 0：有卡片<br />- 1：有卡片|
|depositld|string|卡片ID|

### 通知柜门全部关闭 - 30007

当柜门全部关闭后会通知当前柜子关闭

type: 30007

## 用户相关接口

### 1. 获取用户分页

#### 请求 (10090)

#### 响应 (20090)

返回用户分页数据

|参数名|类型|描述|
| --------------| --------| --------------------------------------|
|id|String|用户ID，存在则为修改，不存在则为新增|
|account|String|用户名|
|realName|String|姓名|
|mobile|String|手机号|
|jobNo|String|员工编号|
|companyId|String|公司ID|
|belongDeptId|String|所属部门ID|
|workDeptId|String|工作部门ID|
|roleIds|Array|角色ID数组|
|gender|String|性别(1男,0女)|
|status|Number|状态(0禁用,1正常)|
|isFreeAccess|string|是否自由存取 0 - 否 1- 是|

### 2. 新增/修改用户

#### 请求 (10091)

|参数名|类型|必填|描述|
| --------------| --------| ------| --------------------------------------|
|id|String|否|用户ID，存在则为修改，不存在则为新增|
|account|String|是|用户名|
|realName|String|是|姓名|
|mobile|String|是|手机号|
|jobNo|String|是|员工编号|
|companyId|String|是|公司ID|
|belongDeptId|String|是|所属部门ID|
|workDeptId|String|是|工作部门ID|
|roleIds|Array|是|角色ID数组|
|gender|String|是|性别(1男,0女)|
|status|Number|是|状态(0禁用,1正常)|
|isFreeAccess|string|是|是否自由存取 0 - 否 1- 是|

#### 响应 (20091)

返回操作结果

### 3. 删除用户

#### 请求 (10093)

|参数名|类型|必填|描述|
| --------| --------| ------| --------|
|id|String|是|用户ID|

#### 响应 (20093)

返回操作结果

### 4. 解锁用户

#### 请求 (10094)

|参数名|类型|必填|描述|
| --------| -------| ------| ------------|
|ids|Array|是|用户ID数组|

#### 响应 (20094)

返回操作结果

### 5. 重置密码

#### 请求 (10095)

|参数名|类型|必填|描述|
| ----------| --------| ------| --------|
|password|String|是|密码|
|id|String|是|用户ID|

#### 响应 (20095)

返回操作结果

### 导入用户，从excel - 10097

请求 - 10097

|参数名|类型|必填|描述|
| --------| --------| ------| ----------|
|path|string|是|文件路径|

响应 - 20097

## 组织

### 获取组织树

请求 (10080)

|参数名|类型|必填|描述|
| ----------| --------| ------| ----------|
|parentId|Number|是|父节点ID|

#### 响应 (20080)

|字段名|类型|描述|
| ------------------| --------| ------------|
|id|String|组织ID|
|organizationName|String|组织名称|
|children|Array|子组织列表|

|字段名|类型|描述|
| ------------------| --------| ----------|
|id|String|部门ID|
|parentId|Number|父组织ID|
|organizationName|String|组织名称|
|organizationKey|String|组织编码|

### 2. 获取绑定的部门ID列表

#### 请求 (10201)

无需参数

#### 响应 (20201)

返回部门ID数组，例如：["部门ID1", "部门ID2"]

### 3. 获取角色列表

#### 请求 (10092)

‍

#### 响应 (20092)

|字段名|类型|描述|
| --------| -------| --------------|
|list|array|角色对象数组|

|字段名|类型|描述|
| ----------| --------| ----------|
|id|String|角色ID|
|roleName|String|角色名称|
|roleCode|String|角色编码|

### 获取部门用户列表 - 10202

**请求类型**: 10202

|参数名|类型|必填|描述|
| --------| -------| ------| ------------|
|orgIds|Array|是|部门ID数组|

**响应类型**: 20202

|字段|类型|说明|
| ------| -------| --------------|
|list|array|用户信息列表|

组员信息

|字段|类型|说明|
| ----------| --------| --------|
|id|string|用户ID|
|account|string|用户名|
|realName|string|姓名|
|mobile|string|手机号|

## 班组基础管理

### 班组对象 (PersonGroup) 

|字段名|类型|描述|
| ---------------| ---------| ----------------------|
|id|String|班组ID|
|groupNo|String|班组编号|
|groupName|String|班组名称|
|accessPattern|Boolean|是否开启班组存取模式|
|companyId|String|公司ID|
|companyName|String|公司名称|
|workDeptId|String|工作部门ID|
|workDeptName|String|工作部门名称|
|creatorName|String|创建人|
|createTime|String|创建时间|

### 获取班组分页 - 10040

**请求类型**: 10040  
**响应类型**: 20040

|字段|类型|说明|
| ---------| --------| --------------|
|list|array|班组对象数组|
|total|number|总记录数|
|current|number|当前页码|

### 保存班组信息 - 10041

**请求类型**: 10041

|参数名|类型|必填|描述|
| ------------| --------| ------| --------------------|
|companyId|String|是|公司ID|
|workDeptId|String|是|工作部门ID|
|id|String|否|班组ID(更新时需要)|
|groupName|String|是|班组名称|

**响应类型**: 20041

### 删除班组 - 100042

**请求类型**: 10042

|参数名|类型|必填|描述|
| --------| --------| ------| --------|
|id|String|是|班组ID|

**响应类型**: 20042

### 开启班组模式 - 10043

**请求类型**: 10043

|参数名|类型|必填|描述|
| --------| --------| ------| --------|
|id|String|是|班组ID|

  
**响应类型**: 20043

### 关闭班组模式 - 10044

**请求类型**: 10044

|参数名|类型|必填|描述|
| --------| --------| ------| --------|
|id|String|是|班组ID|

  
**响应类型**: 20044

## 班组成员管理

### 班组成员对象 (GroupMember)

|字段名|类型|描述|
| -------------| --------| ---------------------------------------------|
|id|String|关联ID|
|personId|String|用户ID|
|groupId|String|班组ID|
|userName|String|用户名|
|displayName|String|姓名|
|mobile|String|手机号|
|type|String|组员类型(GROUP_LEADER:班组长,EMPLOYEE:员工)|

### 获取班组成员分页 - 10050

**请求类型**: 10050

|参数名|类型|必填|描述|
| ---------| --------| ------| ----------|
|current|Number|是|当前页码|
|limit|Number|是|每页条数|
|groupId|Number|是|班组ID|

  
**响应类型**: 20050

|字段|类型|说明|
| ---------| --------| --------------|
|list|array|班组对象列表|
|total|number|总记录数|
|current|number|当前页码|

### 批量保存班组成员 - 10051

**请求类型**: 10051

```markdown
{ "list": [ { "groupId": "班组ID", "personId": "用户ID" } ] }
```

### 删除班组成员 - 10052

**请求类型**: 10052

|参数名|类型|必填|描述|
| --------| --------| ------| --------|
|id|String|是|关联ID|

  
**响应类型**: 20052

### 获取已绑定组员列表 - 10053

**请求类型**: 10053

|字段|类型|说明|
| ------| ------| --------|
|id|int|班组ID|

**响应类型**: 20053

|字段|类型|说明|
| ------| -------| ------------|
|list|array|组员ID列表|

组员信息

|字段|类型|说明|
| ------| ------| --------|
|id|int|用户ID|

### 修改班组成员信息 - 10054

**请求类型**: 10054

|参数名|类型|必填|描述|
| ----------| --------| ------| -------------------------------------------------|
|id|String|否|关联ID(更新时需要)|
|personId|String|是|用户ID|
|groupId|String|是|班组ID|
|type|String|是|组员类型(GROUP\_LEADER:班组长,EMPLOYEE:员工)|

**响应类型**: 20054

## 班表管理

### 班表分页查询 - 10060/10096

**请求类型**: 10060 10096

|参数名|类型|必填|描述|
| ---------| --------| ------| ----------|
|current|Number|是|当前页码|
|limit|Number|是|每页条数|

**响应类型**: 20060 20096

|字段|类型|说明|
| ---------| --------| --------------|
|list|array|班表数组对象|
|total|number|总记录数|
|current|number|当前页码|

|字段|类型|说明|
| --------------| --------| --------------|
|id|string|班表ID|
|scheduleCode|string|班表编号|
|scheduleName|string|班表名称|
|companyName|string|公司名称|
|workDeptName|string|工作部门名称|
|creatorName|string|创建人|
|createTime|string|创建时间|

### 新增班表 - 10061

**请求类型**: 10061

|参数名|类型|必填|描述|
| --------------| --------| ------| --------------|
|scheduleName|String|是|班表名称|
|companyId|String|是|公司ID|
|companyName|String|是|公司名称|
|workDeptId|String|是|工作部门ID|
|workDeptName|String|是|工作部门名称|

  
**响应类型**: 20061

|字段|类型|说明|
| ------| --------| ------------|
|id|string|新增班表ID|

### 删除班表 - 10062

**请求类型**: 10062

|参数名|类型|必填|描述|
| --------| --------| ------| --------|
|id|String|是|班表ID|

  
**响应类型**: 20062

## 排班管理

### 排班管理分页 - 10070

**请求类型**: 10070

|参数名|类型|必填|描述|
| ------------| --------| ------| ----------|
|current|Number|是|当前页码|
|limit|Number|是|每页条数|
|scheduleId|String|是|班表ID|
|displayName|String|否|姓名搜索|
|workTimeStart|String|否|上班时间开始(HH:mm:ss)|
|workTimeEnd|String|否|上班时间结束(HH:mm:ss)|
|workOffTimeStart|String|否|下班时间开始(HH:mm:ss)|
|workOffTimeEnd|String|否|下班时间结束(HH:mm:ss)|
|workDateStart|String|否|工作日期开始(yyyy-MM-dd)|
|workDateEnd|String|否|工作日期结束(yyyy-MM-dd)|

  
**响应类型**: 20070

|字段|类型|说明|
| ---------| --------| ----------|
|total|number|总记录数|
|current|number|当前页码|
|list|array|排班对象|

|字段名|类型|描述|
| --------------| ---------| ----------------------|
|id|String|排班记录ID|
|personId|String|人员ID|
|scheduleId|String|班表ID|
|userName|String|用户名|
|displayName|String|姓名|
|mobile|String|手机号|
|companyName|String|公司名称|
|workDeptName|String|工作部门名称|
|workDate|String|工作日期(yyyy-MM-dd)|
|workTime|String|上班时间(HH:mm:ss)|
|crossDay|Boolean|下班时间是否次日|
|workOffTime|String|下班时间(HH:mm:ss)|

### 新增排班 - 10071

**请求类型**: 10071

|字段名|类型|描述|
| --------| -------| --------------|
|list|Array|排班记录列表|

|字段名|类型|描述|
| -------------| ---------| ----------------------|
|personId|String|人员ID|
|groupId|String|班组ID|
|scheduleId|String|班表ID|
|workDate|String|工作日期(yyyy-MM-dd)|
|workTime|String|上班时间(HH:mm:ss)|
|crossDay|Boolean|下班时间是否次日|
|workOffTime|String|下班时间(HH:mm:ss)|

  
**响应类型**: 20071

### 编辑排班 - 10072

**请求类型**: 10072

|字段名|类型|描述|
| -------------| ---------| ----------------------|
|id|string|排班记录ID|
|personId|String|人员ID|
|scheduleId|String|班表ID|
|workDate|String|工作日期(yyyy-MM-dd)|
|workTime|String|上班时间(HH:mm:ss)|
|crossDay|Boolean|下班时间是否次日|
|workOffTime|String|下班时间(HH:mm:ss)|

  
**响应类型**: 20072

### 删除排班 - 10073

**请求类型**: 10073

|参数名|类型|必填|描述|
| --------| --------| ------| ------------|
|id|String|是|排班记录ID|

**响应类型**: 20073

### 批量删除排班 - 10074

**请求类型**: 10074

|参数名|类型|必填|描述|
| --------| -------| ------| ----------------|
|ids|Array|是|排班记录ID数组|

**响应类型**: 20074

### 排班数据导入 - 10075

**请求类型**: 10075

|字段|类型|说明|
| ------------| --------| -----------------|
|file|File|上传的Excel文件|
|scheduleId|string|班表ID|

  
**响应类型**: 20075

|字段|类型|说明|
| ------------| --------| -----------------|
|file|File|上传的Excel文件|
|scheduleId|string|班表ID|
|success|number|成功导入数量|
|fail|number|失败导入数量|

### 获取可选组员列表 - 10100

**请求类型**: 10100  
**响应类型**: 20100

|字段|类型|说明|
| ----------| --------| --------|
|id|string|用户ID|
|account|string|用户名|
|realName|string|姓名|
|mobile|string|手机号|

‍
