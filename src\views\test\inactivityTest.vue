<!-- 活动监控测试页面 -->
<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-800 to-gray-900 flex flex-col items-center justify-center p-8">
    <!-- 页面标题 -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-white mb-4">活动监控测试页面</h1>
      <p class="text-gray-300 text-lg">测试停止和重新开始活动监控功能</p>
    </div>

    <!-- 状态显示区域 -->
    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 mb-8 w-full max-w-2xl">
      <h2 class="text-2xl font-bold text-blue-300 mb-4 text-center">监控状态</h2>
      
      <!-- 监控状态指示器 -->
      <div class="flex items-center justify-center mb-4">
        <div class="flex items-center space-x-3">
          <div 
            :class="[
              'w-4 h-4 rounded-full transition-all duration-300',
              monitorStatus ? 'bg-green-400 animate-pulse' : 'bg-red-400'
            ]"
          ></div>
          <span class="text-white text-lg font-medium">
            监控状态: {{ monitorStatus ? '运行中' : '已停止' }}
          </span>
        </div>
      </div>

      <!-- 倒计时显示 -->
      <div v-if="countdown > 0" class="text-center mb-4">
        <div class="text-3xl font-mono font-bold text-yellow-300">
          {{ countdown }}
        </div>
        <div class="text-gray-300 text-sm">倒计时剩余秒数</div>
      </div>

      <!-- 监控信息 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div class="bg-gray-700 bg-opacity-50 rounded-lg p-3">
          <div class="text-gray-300">超时时间</div>
          <div class="text-white font-bold">{{ timeoutDuration / 1000 }}秒</div>
        </div>
        <div class="bg-gray-700 bg-opacity-50 rounded-lg p-3">
          <div class="text-gray-300">警告时间</div>
          <div class="text-white font-bold">{{ warningTime / 1000 }}秒</div>
        </div>
      </div>
    </div>

    <!-- 控制按钮区域 -->
    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 mb-8 w-full max-w-2xl">
      <h2 class="text-2xl font-bold text-blue-300 mb-4 text-center">控制面板</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- 启动监控按钮 -->
        <button
          @click="startMonitoring"
          :disabled="monitorStatus"
          class="flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          <i class="fas fa-play text-lg"></i>
          <span class="font-medium">启动监控</span>
        </button>

        <!-- 停止监控按钮 -->
        <button
          @click="stopMonitoring"
          :disabled="!monitorStatus"
          class="flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          <i class="fas fa-stop text-lg"></i>
          <span class="font-medium">停止监控</span>
        </button>

        <!-- 重新启动监控按钮 -->
        <button
          @click="restartMonitoring"
          class="flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105"
        >
          <i class="fas fa-redo text-lg"></i>
          <span class="font-medium">重新启动监控</span>
        </button>

        <!-- 开始倒计时按钮 -->
        <button
          @click="startCountdown"
          :disabled="countdown > 0"
          class="flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100"
        >
          <i class="fas fa-clock text-lg"></i>
          <span class="font-medium">开始倒计时</span>
        </button>
      </div>
    </div>

    <!-- 日志显示区域 -->
    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 w-full max-w-2xl">
      <h2 class="text-2xl font-bold text-blue-300 mb-4 text-center">操作日志</h2>
      
      <div class="bg-black bg-opacity-30 rounded-lg p-4 h-48 overflow-y-auto">
        <div v-for="(log, index) in logs" :key="index" class="text-sm mb-2">
          <span class="text-gray-400">{{ log.time }}</span>
          <span class="text-white ml-2">{{ log.message }}</span>
        </div>
        <div v-if="logs.length === 0" class="text-gray-400 text-center">
          暂无日志记录
        </div>
      </div>
      
      <button
        @click="clearLogs"
        class="mt-4 w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
      >
        清空日志
      </button>
    </div>

    <!-- 返回按钮 -->
    <div class="mt-8">
      <button
        @click="goBack"
        class="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105"
      >
        <i class="fas fa-arrow-left text-lg"></i>
        <span class="font-medium">返回首页</span>
      </button>
    </div>
  </div>
</template>

<script>
import inactivityMonitor from "@/utils/inactivityMonitor";

export default {
  name: "InactivityTest",
  data() {
    return {
      monitorStatus: false,
      timeoutDuration: 10000, // 10秒用于测试
      warningTime: 3000, // 3秒警告时间
      countdown: 0,
      countdownTimer: null,
      logs: []
    };
  },
  
  mounted() {
    this.addLog("页面加载完成");
    this.updateMonitorStatus();
  },
  
  beforeDestroy() {
    // 清理倒计时定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
    this.addLog("页面即将销毁，清理资源");
  },
  
  methods: {
    // 启动监控
    startMonitoring() {
      this.addLog("启动活动监控");
      inactivityMonitor.start(
        // 超时回调
        () => {
          this.addLog("检测到无活动，触发超时回调");
          alert("检测到长时间无活动，将跳转到首页");
          this.$router.push("/");
        },
        this.timeoutDuration,
        // 警告回调
        (remainingSeconds) => {
          this.addLog(`警告：还有 ${remainingSeconds} 秒将触发超时`);
        },
        this.warningTime
      );
      this.updateMonitorStatus();
    },
    
    // 停止监控
    stopMonitoring() {
      this.addLog("停止活动监控");
      inactivityMonitor.stop();
      this.updateMonitorStatus();
    },
    
    // 重新启动监控（模拟 restartInactivityMonitor 方法）
    restartMonitoring() {
      this.addLog("重新启动活动监控");
      // 先停止现有监控
      inactivityMonitor.stop();
      // 重新启动
      this.startMonitoring();
    },
    
    // 开始倒计时（模拟存证页面的倒计时功能）
    startCountdown() {
      this.addLog("开始倒计时，停止活动监控");
      // 停止活动监控
      inactivityMonitor.stop();
      this.updateMonitorStatus();
      
      // 开始倒计时
      this.countdown = 10; // 10秒倒计时
      this.countdownTimer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
          this.handleCountdownEnd();
        }
      }, 1000);
    },
    
    // 倒计时结束处理
    handleCountdownEnd() {
      this.addLog("倒计时结束，重新启动活动监控");
      this.countdown = 0;
      // 重新启动活动监控
      this.restartMonitoring();
    },
    
    // 更新监控状态
    updateMonitorStatus() {
      this.monitorStatus = inactivityMonitor.isEnabled;
    },
    
    // 添加日志
    addLog(message) {
      const now = new Date();
      const time = now.toLocaleTimeString();
      this.logs.unshift({
        time,
        message
      });
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50);
      }
    },
    
    // 清空日志
    clearLogs() {
      this.logs = [];
      this.addLog("日志已清空");
    },
    
    // 返回首页
    goBack() {
      this.addLog("返回首页");
      this.$router.push("/");
    }
  }
};
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}
</style>
