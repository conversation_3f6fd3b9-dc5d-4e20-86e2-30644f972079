<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div
    class="min-h-screen bg-gradient-to-br from-gray-800 to-gray-900 flex flex-col items-center justify-center relative overflow-hidden tech-grid"
  >
    <Logo class="absolute top-8 left-8 z-40 sm:top-8 left-4 sm:left-8 z-40 transform scale-75 sm:scale-100" />
    <!--显示用户信息-->
    <UserInfoBar
      :username="getUserName()"
      :absolute="true"
      class="absolute bottom-4 sm:bottom-8 right-4 sm:right-8 z-40"
    />

    <div class="absolute top-4 sm:top-8 right-4 sm:right-8 z-50 flex gap-2 sm:gap-4">
      <!-- 使用HomeButton组件替代原来的返回主页按钮 -->
      <HomeButton @click="onLogoutSuccess" />

      <!-- 使用LogoutButton组件替代原来的返回登录按钮 -->
      <LogoutButton @confirm="onLogoutSuccess" />
    </div>

    <!-- 现代简约装饰元素 -->
    <div
      class="absolute inset-0 bg-[url('https://ai-public.mastergo.com/ai/img_res/7668f33c5e57ec7fa1f31664082487cb.jpg')] opacity-10 bg-cover"
    ></div>

    <!-- 错误提示信息 -->
    <div
      v-if="isErrorMsg"
      class="relative bg-gradient-to-br from-gray-900 to-gray-800 p-4 sm:p-8 md:p-10 rounded-2xl shadow-[0_0_50px_rgba(0,0,0,0.3)] mb-6 sm:mb-8 md:mb-12 max-w-4xl mx-auto transform hover:scale-[1.02] transition-all duration-500 border border-white/10 backdrop-blur-lg w-11/12 sm:w-auto"
    >
      <div class="flex flex-col sm:flex-row items-center gap-4 sm:gap-8 md:gap-12">
        <!-- 左侧提示图标 -->
        <div class="w-full sm:w-1/4 flex justify-center relative mb-4 sm:mb-0">
          <!-- <div class="absolute -inset-2 bg-red-500/20 blur-lg rounded-full animate-pulse"></div> -->
          <i v-if="isErrorMsg"
            class="fas fa-exclamation-circle text-red-400 text-4xl sm:text-5xl md:text-6xl relative animate-float"
          ></i>
        </div>

        <!-- 右侧文字内容 -->
        <div class="w-full sm:w-3/4 sm:pl-6 md:pl-8 sm:border-l-2 border-gray-700/50">
          <div class="flex items-center gap-4 mb-4 sm:mb-6">
            <div class="relative">
              <div
                class="absolute -inset-1 bg-red-500/30 blur rounded-lg"
              ></div>
              <!-- <i class="fas fa-exclamation-triangle text-red-400 text-3xl relative"></i> -->
              <h3 class="text-white text-xl sm:text-2xl md:text-3xl font-bold tracking-wide">
                温馨提示
              </h3>
            </div>
          </div>
          <p class="text-gray-200 text-base sm:text-lg md:text-xl leading-relaxed font-medium">
            {{ errorMessage }}
          </p>
        </div>
      </div>
      <!-- 底部装饰线 -->
      <div
        class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 via-red-300 to-red-500"
      ></div>
    </div>

    <!-- 顶部提示文字 -->
    <div
      v-if="cabinetNumbers.length > 0 && !isErrorMsg"
      class="text-white text-sm sm:text-base md:text-xl font-medium mb-6 sm:mb-8 md:mb-12 tracking-wider text-center px-4 sm:px-6 md:px-8 py-3 sm:py-4 rounded-lg bg-white bg-opacity-10 backdrop-blur-md border border-white border-opacity-20 shadow-xl w-11/12 max-w-3xl"
    >
      <div class="flex items-center justify-center space-x-2 sm:space-x-3">
        <i class="fas fa-info-circle text-blue-300 text-lg sm:text-xl md:text-2xl" v-if="cabinetNumbers.length > 0 && !isErrorMsg"></i>
        <span>请将证件存入对应柜格号，存入证件后请及时关闭柜门</span>
      </div>
    </div>

    <!-- v-if="!isErrorMsg" -->
    <!-- 柜格号显示区域 -->
    <div class="relative mb-8 sm:mb-12 md:mb-16 w-11/12 sm:w-full max-w-3xl px-2 sm:px-4" :key="cabinetNumbersKey">
      <div
        class="bg-white bg-opacity-5 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white border-opacity-10 shadow-2xl"
      >
        <div class="flex justify-center items-center mb-4">
          <h3 class="text-blue-300 text-base sm:text-lg font-medium text-center">
            柜格号分配
          </h3>
          <!-- 滚动控制按钮 -->
          <div
            v-if="showScrollControls"
            class="absolute right-4 sm:right-6 flex space-x-2"
          >
            <button
              @click="scrollCabinet('up')"
              :disabled="!canScrollUp"
              class="p-1.5 sm:p-2 rounded-full bg-gray-700 bg-opacity-50 text-blue-300 hover:bg-opacity-70 transition-colors"
              title="向上滚动"
            >
              <i class="fas fa-chevron-up text-xs"></i>
            </button>
            <button
              @click="scrollCabinet('down')"
              :disabled="!canScrollDown"
              class="p-1.5 sm:p-2 rounded-full bg-gray-700 bg-opacity-50 text-blue-300 hover:bg-opacity-70 transition-colors"
              title="向下滚动"
            >
              <i class="fas fa-chevron-down text-xs"></i>
            </button>
          </div>
        </div>
        <div class="cabinet-container" ref="cabinetContainer">
          <div
            class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 sm:gap-4 md:gap-6 lg:gap-8"
          >
            <div
              v-for="(num, index) in cabinetNumbers"
              :key="index"
              class="text-center cabinet-item"
            >
              <div class="relative inline-block">
                <div
                  class="absolute inset-0 bg-blue-500 rounded-full opacity-20 blur-md"
                ></div>
                <div
                  class="relative text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-blue-300 tracking-wider bg-gray-800 bg-opacity-50 rounded-full w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 lg:w-20 lg:h-20 flex items-center justify-center mx-auto"
                >
                  {{ num.gridName }}
                </div>
              </div>
              <div
                class="text-gray-300 text-xs sm:text-sm mt-2 sm:mt-3 md:mt-4 uppercase tracking-wider"
              >
                Cabinet {{ num.gridName }}
              </div>
            </div>
          </div>
        </div>
        <!-- 滚动指示器 -->
        <div
          v-if="showScrollControls"
          class="flex justify-center mt-3 sm:mt-4 space-x-1"
        >
          <div
            v-for="i in scrollDots"
            :key="i"
            :class="[
              'w-1.5 h-1.5 rounded-full transition-all',
              currentScrollDot === i
                ? 'bg-blue-400 w-3'
                : 'bg-gray-500 bg-opacity-50',
            ]"
          ></div>
        </div>
      </div>
    </div>
    <!-- 倒计时区域 -->

     <!-- 重新开门按钮 -->
 
    <div v-show="!!reOpen && !isErrorMsg" :key="reOpenKey" class="mt-6 sm:mt-8">
      <button
        @click="handleReOpen"
        class="flex items-center justify-center space-x-2 px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105"
      >
        <i class="fas fa-door-open text-lg sm:text-xl"></i>
        <span class="text-base sm:text-lg font-medium">重新开门</span>
      </button>
    </div>

    <!-- 柜门未关闭提示 -->
    <div
      v-if="showTip"
      class="text-red-500 text-base sm:text-lg mt-4 flex items-center justify-center space-x-2 animate-pulse"
    >
      <i class="fas fa-exclamation-circle text-lg sm:text-xl"></i>
      <span>柜门未关闭，请重新关闭</span>
    </div>

    <div class="relative w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48" v-if="!reOpen && !isErrorMsg">
      <div
        class="absolute inset-0 rounded-full bg-white bg-opacity-5 backdrop-blur-sm border border-white border-opacity-10 shadow-xl"
      ></div>
      <svg class="w-full h-full" viewBox="0 0 100 100">
        <!-- 背景圆环 -->
        <circle
          cx="50"
          cy="50"
          r="40"
          fill="none"
          stroke="rgba(255,255,255,0.1)"
          stroke-width="4"
        />
        <!-- 进度条圆环 -->
        <circle
          cx="50"
          cy="50"
          r="40"
          fill="none"
          stroke="url(#progressGradient)"
          stroke-width="4"
          stroke-linecap="round"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="dashOffset"
          transform="rotate(-90 50 50)"
        />
        <!-- 渐变定义 -->
        <defs>
          <linearGradient
            id="progressGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="100%"
          >
            <stop offset="0%" stop-color="#3B82F6" />
            <stop offset="100%" stop-color="#10B981" />
          </linearGradient>
        </defs>
      </svg>

      <!-- 倒计时数字 -->
      <div
        class="absolute inset-0 flex flex-col items-center justify-center space-y-1"
      >
        <div class="text-xl sm:text-2xl md:text-3xl font-mono font-bold text-white tracking-wider">
          {{ countdown }}
        </div>
        <div class="text-gray-300 text-xs uppercase tracking-widest">
          Seconds
        </div>
      </div>
    </div>
    <!-- 底部提示 -->
    <div
      v-if="!reOpen"
      class="text-gray-300 text-xs sm:text-sm mt-4 sm:mt-6 md:mt-8 flex items-center space-x-2"
    >
      <i v-if="!reOpen" class="fas fa-clock text-blue-300"></i>
      <span>倒计时结束后柜门需要关闭</span>
    </div>

    <!-- 简约粒子背景 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div
        v-for="i in 20"
        :key="'particle-' + i"
        class="absolute rounded-full bg-white opacity-5"
        :style="{
          left: Math.random() * 100 + '%',
          top: Math.random() * 100 + '%',
          width: Math.random() * 4 + 1 + 'px',
          height: Math.random() * 4 + 1 + 'px',
          animation: `float ${Math.random() * 8 + 4}s ease-in-out infinite`,
        }"
      ></div>
    </div>
  </div>
</template>
<script>
import UserInfoBar from "@/components/UserInfoBar.vue";
import storage from "@/utils/storage";
import LogoutButton from "@/components/LogoutButton.vue";
import HomeButton from "@/components/HomeButton.vue";
import Logo from "@/components/Logo.vue";
import inactivityMonitor from "@/utils/inactivityMonitor"; // 引入活动监控工具
export default {
  name: "saveCertificate",
  components: {
    UserInfoBar,
    HomeButton,
    LogoutButton,
    Logo,
  },
  inject: ["speak", "websocketService", "websocketMsg", "speakSync"],
  data() {
    return {
      cabinetNumbersKey:0,
      cabinetNumbers: [],
      countdown: 90,
      countdownConfig: 90,
      timer: null,
      circumference: 2 * Math.PI * 40,
      currentScrollDot: 1, // 当前滚动位置
      isScrolling: false, // 是否正在滚动中
      reOpen: false, //是否重启
      reOpenKey:0,
      hasNeedCardNum: [],
      showTip: false, //规格没有关闭 提示
      speakTimer: null, // 无限循环播报定时器
      isErrorMsg: false, //是否显示错误信息
      errorMessage: "", //错误信息
    };
  },

  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息存证消息:", newMessage);
        if (newMessage) {
          this.handleSaveMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },

  computed: {
    dashOffset() {
      const progress = this.countdown / this.countdownConfig;
      return this.circumference * (1 - progress);
    },
    // 根据柜格号数量计算滚动点数量
    scrollDots() {
      // 如果柜格号数量少于12个，只显示1个点
      // 每增加8个柜格号，增加一个滚动点，但最多不超过5个点
      const itemCount = this.cabinetNumbers.length;
      if (itemCount <= 12) return 1;
      return Math.min(5, Math.ceil((itemCount - 12) / 8) + 1);
    },
    // 计算是否显示滚动控制和指示器
    showScrollControls() {
      return this.scrollDots > 1;
    },
    // 计算是否可以向上滚动
    canScrollUp() {
      return this.currentScrollDot > 1;
    },
    // 计算是否可以向下滚动
    canScrollDown() {
      return this.currentScrollDot < this.scrollDots;
    },
  },
  created() {
    // 获取路由参数
    this.routeParams = this.$route.params;
    // 从本地存储获取倒计时配置值
    const countdownConfig = storage.local.get("countdownConfig");
    // 如果存在配置值则使用，否则保持默认值90
    if (countdownConfig) {
      this.countdown = countdownConfig;
      this.countdownConfig = countdownConfig;
    }
  },
  mounted() {
    this.iscabinetFull(); //柜格是否满
    //
    // 使用nextTick确保DOM已挂载
    this.$nextTick(() => {
      // 监听柜格容器的滚动事件，更新指示器
      if (this.$refs.cabinetContainer) {
        this.$refs.cabinetContainer.addEventListener(
          "scroll",
          this.handleScroll
        );

        // 检查是否需要显示滚动控制
        this.checkScrollable();

        // 监听窗口大小变化，重新计算滚动点
        window.addEventListener("resize", this.checkScrollable);
      }
    });
  },
  beforeDestroy() {
      this.stopMonitoring();
    console.log("组件即将销毁，清理资源...");
    // 清除倒计时定时器
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
      console.log("倒计时定时器已清除");
    }

    // 确保清除语音播报定时器
    if (this.speakTimer) {
      clearInterval(this.speakTimer);
      this.speakTimer = null;
      console.log("语音播报定时器已清除");
    }

    // 取消所有待播放的语音
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
      console.log("组件销毁时已取消所有待播放的语音");
    }

    // 移除滚动事件监听器
    if (this.$refs.cabinetContainer) {
      this.$refs.cabinetContainer.removeEventListener(
        "scroll",
        this.handleScroll
      );
      console.log("滚动事件监听器已移除");
    }

    // 移除窗口大小变化监听器
    window.removeEventListener("resize", this.checkScrollable);
    console.log("窗口大小变化监听器已移除");
  },
  methods: {
       stopMonitoring(){
       let order = {
        type: 10400,
        id: "stopMonitoring",
      };
      this.websocketService.send(order);
    },
    // 获取用户名称
    getUserName() {
      // 直接从 routeParams 中获取 account
      if (this.routeParams && this.routeParams.account) {
        return this.routeParams.account;
      }
      // 兼容可能的嵌套结构
      if (
        this.routeParams &&
        this.routeParams.data &&
        this.routeParams.data.account
      ) {
        return this.routeParams.data.account;
      }
      return "未登录用户";
    },
    //存证消息
    async handleSaveMessages(message) {
      if (!message) return;
      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        switch (message.messages.type) {
          case 20004: // 判断现在柜格是否满响应
            if (message.messages.data.grid_is_full) {
              this.speak("当前柜格已满了");
            } else {
              this.speak("开始分配柜格");
              this.startDistribution();
            }
            break;

          case 20005: //分配柜格响应
            if (message.messages.data.code == 1) {
              this.isErrorMsg = false;
              this.errorMessage = "";
              this.speak("请将证件存入柜格");
              this.startCountdown();
              this.cabinetNumbers = (message?.messages?.data?.list || []).map(
                (item) => {
                  return {
                    gridCode: item.gridCode,
                    gridName: item.gridName,
                  }
                }
              );
              this.cabinetNumbersKey++

            } else {
              this.isErrorMsg = true;
              this.errorMessage = message.messages.data.msg;
              // 报错信息开始
              await this.speakSync(message.messages.data.msg);
              // 等待语音播报完成后再跳转
              await new Promise((resolve) => setTimeout(resolve, 3000)); // 增加1秒延迟确保语音播完
              this.$router.push({
                name: "actionType",
                params: { data: { ...this.routeParams } },
              });
            }
            // 报错信息结束
            break;
          case 20006: //查询柜格响应
            if (message.messages.data.status === 0) {
              this.speak(`${message.messages.data.gridCode}柜格未关闭 请关闭`);
              this.showTip = true;
              // this.reOpen = false;
            } else {
              this.showTip = false;
              this.speak(`${message.messages.data.gridCode}柜格已关闭`);
              console.log(1)
              this.reOpen = false;
              this.countdown = this.countdownConfig;
            }

            console.log("message.messages", message.messages);
            break;
          case 20007: //柜格响应
            this.speak("柜格已经重新打开");
           
            this.countdown = this.countdownConfig;
            console.log(2)
            this.reOpen = false;
            this.startCountdown();
            break;

          case 30005: //通知存证状态
            console.log("30005 message.messages", message.messages);

            var hasCardNum = (message?.messages?.data?.list || []).filter(
              (item) => item.hasCard === 0
            );

            if (hasCardNum.length === 0) {
              //存证
              console.log(3)
              this.reOpen = false;
              // 确保停止语音播报
              this.speakSync("存证成功");
                console.log('30005cabinetNumbers')
              this.cabinetNumbers = [];
              this.cabinetNumbersKey++
              this.hasNeedCardNum = [];
              if (this.timer) {
              clearInterval(this.timer);
              this.timer = null;
            }

            // 确保清除语音播报定时器
            if (this.speakTimer) {
              clearInterval(this.speakTimer);
              this.speakTimer = null;
            }

            // 取消所有待播放的语音
            if (window.speechSynthesis) {
              window.speechSynthesis.cancel();
            }

         
              setTimeout(() => {
                this.$router.push({
                  path: "/",
                });
              }, 2000);
            } else {
           
              this.hasNeedCardNum = (message?.messages?.data?.list || [])
                .filter(item => item.hasCard === 0) // 过滤出hasCard为0的项
                .map(item => {
                  return {
                    gridCode: item.gridCode,
                    gridName: item.gridName,
                  }
                }); // 提取gridCode值
                console.log("this.hasNeedCardNum",this.hasNeedCardNum)
                this.cabinetNumbers = JSON.parse(JSON.stringify(this.hasNeedCardNum));
                this.cabinetNumbersKey++
               this.reOpen = true;
               this.reOpenKey++
               this.speak(
                   "未检测到柜内有证件 请确认是否已放置证件 点击下方按钮重新开门操作"
                 );
            }
            break;
          case 30006: {
            // 通知柜门关闭了
            // this.speak(message?.messages?.data.msg);
            this.speak(`柜格号${message?.messages?.data.gridName}柜门已关门`);
            const gridCodeToRemove = message?.messages?.data.gridCode;
            if (gridCodeToRemove) {
              this.cabinetNumbers = this.cabinetNumbers.filter(
                (code) => code !== gridCodeToRemove
              );
              this.cabinetNumbersKey++

            }
            break;
          }

          case 30007: // 柜门全部关闭通知
            // this.speak("柜门已全部关闭");
            if (this.timer) {
              clearInterval(this.timer);
              this.timer = null;
            }

            // 确保清除语音播报定时器
            if (this.speakTimer) {
              clearInterval(this.speakTimer);
              this.speakTimer = null;
            }


      
            break;

          case 30008: // 通知存证异常
            this.speak(message?.messages?.data.msg);
            // 重置倒计时
            if (this.timer) {
              clearInterval(this.timer);
              this.timer = null;
              console.log("倒计时定时器已清除");
              const countdownConfig = storage.local.get("countdownConfig");
              if (countdownConfig) {
                this.countdown = countdownConfig;
                this.countdownConfig = countdownConfig;
              } else {
                this.countdown = 90;
                this.countdownConfig = 90;
              }
            }
            this.startCountdown(); // 重新开始倒计时
            break;

          default:
          // console.log("未处理的消息类型:", message);
        }
      }
    },

    async iscabinetFull() {
      //请求判断现在柜格是否满
      const userInfo = await storage.local.get("userInfo");
      let order = {
        type: 10004,
        id: "iscabinetFull",
        data: {
          username: userInfo.account,
          account: userInfo.account,
        },
      };
      this.websocketService.send(order);
    },

    async startDistribution() {
      //请求请求分配柜格
      const userInfo = await storage.local.get("userInfo");
      let order = {
        type: 10005,
        id: "startDistribution",
        data: {
          username: userInfo.account,
          account: userInfo.account,
        },
      };
      this.websocketService.send(order);
    },
    async queryGridStutas() {
      if (this.cabinetNumbers.length > 0) {
        const userInfo = await storage.local.get("userInfo");
        let order = {
          type: 10006,
          id: "queryGridStutas",
          data: {
            gridCode: this.cabinetNumbers[0].gridCode,
            username: userInfo.account,
            account: userInfo.account,
          },
        };
        this.websocketService.send(order);
      } else {
        console.log("查询打开柜格异常");
      }
    },

    async handleReOpen(gridCode) {
      console.log("gridCode", gridCode);
      if (this.cabinetNumbers.length > 0) {
        const userInfo = await storage.local.get("userInfo");
        let order = {
          type: 10007,
          id: "reOpen",
          data: {
            gridList: this.hasNeedCardNum?.map(item => item.gridCode) || [],
            // username: userInfo.account,
            account: userInfo.account,
          },
        };
        this.websocketService.send(order);
      } else {
        this.speak("重新打开柜格异常");
        this.cabinetNumbers = [];
        this.hasNeedCardNum = [];

      }
    },

    // 返回首页
    goHome() {
      this.$router.push({
        name: "actionType",
        params: { data: { ...this.routeParams } },
      });
    },

    startCountdown() {
      // 停止活动监控
      alert("停止已停止活动监控")
      inactivityMonitor.stop();
      console.log("倒计时开始，已停止活动监控");

      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(this.timer);
          // 倒计时结束后的处理逻辑
          this.handleCountdownEnd();
        }
      }, 1000);
    },

    // 倒计时结束处理
    handleCountdownEnd() {
      // 防止重复触发，先检查timer是否已经被清除
      if (!this.timer) {
        console.log("倒计时已经结束，避免重复触发");
        return;
      }
      // 清除倒计时定时器
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        console.log("倒计时定时器已清除");
      }

      // 设置状态标记，表示倒计时已结束
      this.countdown = 0;

      // 重新启动活动监控
      alert("开始活动监控")
      this.restartInactivityMonitor();

      // 只有在没有启动语音播报的情况下才启动
      if (!this.speakTimer) {
        console.log("开始播报关闭柜门提示");
        this.startInfiniteSpeak("请关闭柜门", 3000);
      } else {
        this.stopInfiniteSpeak();
        console.log("语音播报已在运行中，避免重复启动");
      }
    },

    // 检查是否需要滚动
    checkScrollable() {
      const container = this.$refs.cabinetContainer;
      if (!container) return;

      // 如果内容高度小于容器高度，则不需要滚动，强制设置滚动点为1
      if (container.scrollHeight <= container.clientHeight) {
        this.currentScrollDot = 1;
      }
    },

    // 处理滚动事件
    handleScroll() {
      if (this.isScrolling) return; // 如果正在程序控制滚动，不处理用户滚动事件

      const container = this.$refs.cabinetContainer;
      const scrollPercentage =
        container.scrollTop / (container.scrollHeight - container.clientHeight);

      // 根据滚动百分比计算当前点
      this.currentScrollDot = Math.min(
        this.scrollDots,
        Math.max(1, Math.ceil(scrollPercentage * this.scrollDots))
      );
    },

    // 控制柜格号容器滚动
    scrollCabinet(direction) {
      if (!this.$refs.cabinetContainer) return;

      const container = this.$refs.cabinetContainer;

      // 确保滚动前先暂停滚动事件处理
      this.isScrolling = true;

      // 根据方向计算滚动位置
      let targetDot =
        direction === "up"
          ? Math.max(1, this.currentScrollDot - 1)
          : Math.min(this.scrollDots, this.currentScrollDot + 1);

      // 计算需要滚动到的位置
      const scrollHeight = container.scrollHeight - container.clientHeight;
      const targetScroll =
        scrollHeight * ((targetDot - 1) / (this.scrollDots - 1 || 1));

      // 设置滚动位置
      container.scrollTo({
        top: targetScroll,
        behavior: "smooth",
      });

      // 更新当前滚动点
      this.currentScrollDot = targetDot;

      // 滚动完成后恢复滚动事件处理
      setTimeout(() => {
        this.isScrolling = false;
      }, 500);
    },

    // 更新滚动指示器
    updateScrollDot(change) {
      let newDot = this.currentScrollDot + change;
      if (newDot < 1) newDot = 1;
      if (newDot > this.scrollDots) newDot = this.scrollDots;
      this.currentScrollDot = newDot;
    },

    /**
     * 开始无限循环播报
     * @param {string} message - 要播报的消息
     * @param {number} interval - 播报间隔时间(毫秒)，默认3000ms
     */
    startInfiniteSpeak(message = "请关闭柜门", interval = 3000) {
      // 防止重复启动，先检查是否已经有定时器在运行
      if (this.speakTimer) {
        console.log("已有语音播报定时器在运行，先停止它");
        this.stopInfiniteSpeak();
      }

      console.log("开始无限循环播报:", message);

      try {
        // 确保清除可能的语音队列
        if (window.speechSynthesis) {
          window.speechSynthesis.cancel();
        }

        // 立即播报一次
        this.speak(message);

        // 设置定时器，定期播报
        this.speakTimer = setInterval(() => {
          console.log("循环播报中...");
          // 播报前先取消可能存在的队列，确保新的语音能够播放
          if (window.speechSynthesis) {
            window.speechSynthesis.cancel();
          }
          this.speak(message);
        }, interval);

        console.log("无限循环播报定时器ID:", this.speakTimer);
      } catch (error) {
        console.error("启动语音播报出错:", error);
        // 确保在出错时清除定时器状态
        this.speakTimer = null;
      }
    },

    /**
     * 停止无限循环播报
     */
    stopInfiniteSpeak() {
      console.log("尝试停止语音播报，当前定时器ID:", this.speakTimer);
      try {
        if (this.speakTimer) {
          clearInterval(this.speakTimer);
          this.speakTimer = null;
          console.log("语音播报已停止");

          // 新增：取消所有待播放的语音
          if (window.speechSynthesis) {
            window.speechSynthesis.cancel();
            console.log("已取消所有待播放的语音队列");
          }
        }
      } catch (error) {
        console.error("停止语音播报出错:", error);
        // 确保定时器被清除
        this.speakTimer = null;

        // 新增：在出错的情况下也尝试取消语音队列
        if (window.speechSynthesis) {
          window.speechSynthesis.cancel();
        }
      }
    },

    // 登出成功处理
    onLogoutSuccess() {
      // 确保在登出前停止语音播报
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        console.log("倒计时定时器已清除");
      }

      // 确保清除语音播报定时器
      if (this.speakTimer) {
        clearInterval(this.speakTimer);
        this.speakTimer = null;
        console.log("语音播报定时器已清除");
      }

      // 取消所有待播放的语音
      if (window.speechSynthesis) {
        window.speechSynthesis.cancel();
        console.log("组件销毁时已取消所有待播放的语音");
      }
      // 登出后的其他处理逻辑会由LogoutButton组件处理
    },

    /**
     * 重新启动活动监控
     */
    restartInactivityMonitor() {
      // 从父组件获取活动监控的配置
      const app = this.$parent;
      if (app && app.times && app.startInactivityMonitor) {
        console.log("倒计时结束，重新启动活动监控");
        app.startInactivityMonitor();
      } else {
        console.log("无法获取活动监控配置，尝试直接启动");
        // 如果无法从父组件获取配置，使用默认配置
        inactivityMonitor.start(
          () => {
            console.log("自动跳转到首页");
            this.$router.push("/");
          },
          300000, // 默认5分钟
          (remainingSeconds) => {
            console.log(remainingSeconds);
          },
          30000 // 警告提前时间30秒
        );
      }
    },
  },
  // 添加路由导航守卫，确保在离开页面前停止语音播报
  beforeRouteLeave(to, from, next) {
    console.log("路由即将离开，清理资源...");

    // 停止语音播报
    if (this.speakTimer) {
      this.stopInfiniteSpeak();
      console.log("路由离开前停止语音播报");
    }

    // 即使没有定时器也尝试取消语音队列
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
      console.log("路由离开前取消所有语音队列");
    }

    // 清除倒计时定时器
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
      console.log("路由离开前清除倒计时定时器");
    }

    next();
  },
};
</script>
  <style scoped>
@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-10px) translateX(5px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 柜格号容器滚动样式 */
.cabinet-container {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 8px;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(255, 255, 255, 0.1);
  scroll-behavior: smooth; /* 添加平滑滚动效果 */
}

/* 响应式调整 */
@media (min-width: 480px) {
  .cabinet-container {
    max-height: 220px;
  }
}

@media (min-width: 640px) {
  .cabinet-container {
    max-height: 250px;
  }
}

@media (min-width: 768px) {
  .cabinet-container {
    max-height: 280px;
  }
}

@media (min-width: 1024px) {
  .cabinet-container {
    max-height: 350px;
  }
}

@media (min-width: 1280px) {
  .cabinet-container {
    max-height: 400px;
  }
}

/* 在触摸屏设备上优化滚动体验 */
@media (hover: none) {
  .cabinet-container {
    -webkit-overflow-scrolling: touch; /* 在iOS设备上实现平滑滚动 */
    padding-right: 0; /* 触摸设备上移除右侧padding */
  }
}

/* 自定义滚动条样式 */
.cabinet-container::-webkit-scrollbar {
  width: 6px;
}

.cabinet-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.cabinet-container::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 10px;
}

.cabinet-container::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* 柜格项悬停效果 */
.cabinet-item {
  transition: transform 0.2s ease-in-out, opacity 0.2s ease;
}

.cabinet-item:hover {
  transform: translateY(-3px);
  opacity: 0.9;
}

/* 触摸设备上禁用悬停效果 */
@media (hover: none) {
  .cabinet-item:hover {
    transform: none;
    opacity: 1;
  }
}

.user-info-bar {
  background: rgba(28, 31, 55, 0.7);
  border-radius: 4px;
  padding: 8px 12px;
}

/* 小屏幕设备的额外优化 */
@media (max-height: 700px) {
  .cabinet-container {
    max-height: 180px;
  }
}

/* 针对超宽屏幕的优化 */
@media (min-width: 1536px) {
  .cabinet-container {
    max-height: 450px;
  }
}

/* 针对超窄屏幕的优化 */
@media (max-width: 360px) {
  .cabinet-container {
    max-height: 180px;
  }
}
</style>
  