 <!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div
    class="min-h-screen h-screen bg-gray-900 tech-grid flex items-center justify-center"
    style="overflow: hidden"
  >
    <Logo class="absolute top-8 left-8 z-40 sm:top-8 left-4 sm:left-8 z-40 transform scale-75 sm:scale-100" />

    <UserInfoBar class="absolute bottom-8 right-8 z-40" />
    <!-- 返回按钮 -->

    <div class="absolute top-8 right-8 z-50 flex gap-4">
      <!-- 使用LogoutButton组件替代原来的返回登录按钮 -->
      <BackButton @click="goBack" :routeName="'Method'" />
    </div>

    <div class="w-[1440px] min-h-[1024px] relative">
      <!-- 主要内容区 -->
      <div
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <!-- 识别框容器 -->
        <div class="relative w-[500px] h-[500px]">
          <!-- 扫描动画 -->
          <div class="absolute inset-0 border-2 border-blue-400/50">
            <div
              class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-400 to-transparent transform animate-scan"
            ></div>
          </div>
          <!-- L型装饰 -->
          <div class="absolute top-0 left-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute top-0 left-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute top-0 right-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute top-0 right-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute bottom-0 left-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute bottom-0 left-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute bottom-0 right-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute bottom-0 right-0 w-2 h-16 bg-blue-400"></div>
          <!-- 中心识别区域 -->
          <div
            v-show="!isCameraActive"
            class="absolute inset-8 border border-gray-600 bg-gray-800/50 flex items-center justify-center"
          >
            <div class="text-center">
              <i class="fas fa-user-circle text-6xl text-blue-400 mb-4"></i>
              <p class="text-gray-300 text-lg">{{ statusMessage }}</p>
            </div>
          </div>

          <div class="w-full h-full">
            <img
              :src="'data:image/jpeg;base64,' + capturedImage"
              class="w-full h-full object-cover"
            />
          </div>
        </div>

        <!-- 新增文字提示框 -->
        <div
          class="mt-8 w-[500px] bg-gray-800/80 backdrop-blur-sm border border-blue-400/30 rounded-lg p-4 shadow-lg transform transition-all duration-300 hover:shadow-blue-400/20"
        >
          <div class="flex items-center">
            <div
              class="flex-shrink-0 w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center"
            >
              <i class="fas fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-4 flex-1">
              <h3 class="text-xl font-bold text-blue-300">{{ tipTitle }}</h3>
              <p
                class="mt-2 text-base text-gray-300 font-medium"
                style="color: red"
              >
                {{ tipSubMessage }}
              </p>
            </div>
          </div>
          <!-- <div class="mt-3 bg-blue-900/20 rounded px-3 py-2 text-xs text-gray-400" style="color: red; font-weight: bold;">
            <span class="text-blue-400" >提示：</span>{{ tipSubMessage }}
          </div> -->
        </div>
      </div>
    </div>

    <!-- 使用弹窗组件 -->
    <!-- <SystemModal
      v-model="showModal"
      title="操作失败"
      :message="modelMsg"
      type="error"
      @close="handleModalClose"
    /> -->
  </div>
</template>
  <script>
import UserInfoBar from "@/components/UserInfoBar.vue";
import storage from "@/utils/storage";
import BackButton from "@/components/BackButton.vue";
import Logo from "@/components/Logo.vue";
// import SystemModal from '@/components/SystemModal.vue';
export default {
  name: "Face-page",
  components: {
    UserInfoBar,
    BackButton,
    Logo,
    // SystemModal
  },
  data() {
    return {
      isPlaying: false,
      showModal: false,
      modelMsg: "",
      isGoHome: false,
      faceParams: {},
      videoDevices: [],
      selectedDeviceId: "",
      stream: null,
      statusMessage: "请将人脸对准摄像头",
      isCameraActive: false,
      showCapturedImage: false,
      capturedImage: "",
      // 新增提示框文字内容
      tipTitle: "人脸识别提示",
      tipMessage: "请保持面部在识别框内，并保持光线充足",
      tipSubMessage: "识别过程中请勿频繁移动，确保面部特征清晰可见",
      // 30002人脸识别结果通知的最后播放时间
      lastFaceResultSpeakTime: 0,
    };
  },
  inject: ["speak", "websocketService", "websocketMsg", "speakSync"],
  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息face:", newMessage);
        if (newMessage) {
          this.handleFaceRecognitionMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // 获取路由参数中的loginType
    this.loginType = this.$route.query.loginType;
    console.log("loginType:", this.loginType);
  },
  mounted() {
    this.sendOrder();
  },

  destroyed() {
    this.closeCamera();
  },
  methods: {
    // 显示弹窗
    openErrorModal() {
      this.showModal = true;
    },
    // 弹窗关闭回调
    handleModalClose() {
      console.log("弹窗已关闭");
      // 可以在这里处理弹窗关闭后的逻辑
      this.goBack();
    },
    sendOrder() {
      //请求人脸识别请求
      const order = {
        type: 10002,
        id: "faceRecognition",
        data: {},
      };
      this.websocketService.send(order);
    },
    endFace() {
      //请求人脸识别结束
      const order = {
        type: 10023,
        id: "endFace",
        data: {},
      };
      this.websocketService.send(order);
    },

    async getVideoDevices() {
      try {
        // const devices = await navigator.mediaDevices.enumerateDevices();
        // console.log("devices", devices);
        // this.videoDevices = devices.filter(
        //   (device) => device.kind === "videoinput"
        // );
        // console.log("this.videoDevices", this.videoDevices);
      } catch (error) {
        // console.error("获取摄像头列表失败:", error);
      }
    },
    async startCamera() {
      try {
        // this.statusMessage = "正在打开摄像头...";
        this.isProcessing = true;
        // console.log("selectedDeviceId",selectedDeviceId)
        // this.speak("正在打开摄像头");
        // 添加语音提示
        // 重点：根据用户选择的摄像头ID打开
        // this.stream = await navigator.mediaDevices.getUserMedia({
        //   video: {
        //     deviceId: selectedDeviceId
        //       ? { exact: selectedDeviceId }
        //       : undefined,
        //   },
        //   audio: false,
        // });
        this.$nextTick(() => {
          // if(this.$refs.video)this.$refs.video.srcObject = this.stream;
          // this.statusMessage = "摄像头已准备就绪";
          // this.speakSync("摄像头已准备就绪请将人脸对准摄像头"); // 进入页面时播报提示语
        });
      } catch (error) {
        this.isProcessing = false;
        // this.statusMessage = `摄像头初始化失败: ${error.message}`;
        // this.speak("摄像头初始化失败，请检查摄像头权限"); // 添加错误语音提示
      }
    },

    closeCamera() {
      this.isCameraActive = false;
      if (this.stream) {
        // 获取所有轨道并逐个关闭
        // this.stream.getTracks().forEach((track) => track.stop());
        // this.stream = null;
        this.isCameraActive = false;
        // this.statusMessage = "摄像头已关闭";
      }
      // if (this.$refs.video) {
      //   this.$refs.video.srcObject = null;
      // }
    },
    goBack() {
      this.isGoHome = true;
      this.endFace();
    },
    jumpActionType(){
      if (this.isGoHome) {
              return;
            }
            var savedLoginType = storage.local.get("loginType");
            if (savedLoginType) {
              this.loginType = savedLoginType;
            }

            if (
              this.loginType === "FACE" ||
              this.loginType === "FACE_OR_FINGERPRINT"
            ) {
              this.$router.push({
                name: "actionType",
                params: { data: { ...this.faceParams, isFace: true } },
              });
              storage.local.set("userInfo", {
                ...this.faceParams,
                isFace: true,
              });
            }

            if(this.loginType === "FACE_AND_FINGERPRINT"){
              storage.local.set("userInfo", {
                  ...this.faceParams,
                  isFace: true,
                  loginType: this.loginType,
                });
                   this.$router.push({
                  name: "actionType",
                  params: {
                    data: {
                      ...this.faceParams,
                      isFace: true,
                      loginType: this.loginType,
                    },
                  },
                });
           
            }

            // if (this.loginType === "FACE_AND_FINGERPRINT") {
            //   storage.local.set("faceAccount", this.faceParams.account);
            // }

            // if (
            //   this.loginType === "FACE_AND_FINGERPRINT" &&
            //   this.$route.params?.data?.isVein
            // ) {
            //   // 验证是否是同一个人
            //   // 将人脸识别账号信息存储到本地存储中

            //   const veinAccount = storage.local.get("veinAccount");
            //   console.log(
            //     "已保存人脸账号:",
            //     this.faceParams.account,
            //     veinAccount
            //   );
            //   if (veinAccount === this.faceParams.account) {
            //     console.log("7111111", this.faceParams);
            //     console.log("3")
            //     this.$router.push({
            //       name: "actionType",
            //       params: {
            //         data: {
            //           ...this.faceParams,
            //           isFace: true,
            //           loginType: this.loginType,
            //         },
            //       },
            //     });
            //     storage.local.set("userInfo", {
            //       ...this.faceParams,
            //       isFace: true,
            //       loginType: this.loginType,
            //     });
            //     storage.local.remove("faceAccount");
            //     storage.local.remove("veinAccount");
            //   } else {
            //     this.speakSync("验证失败，请确保是同一个人");
            //     this.tipSubMessage = "验证失败，请确保是同一个人";
            //     storage.local.remove("faceAccount");
            //     storage.local.remove("veinAccount");
            //     setTimeout(() => {
            //       this.$router.push({ name: "Method" });
            //     }, 2000);
            //   }
            // } else if (
            //   this.loginType === "FACE_AND_FINGERPRINT" &&
            //   !this.$route.params?.data?.isVein
            // ) {
            //   console.log("********", this.faceParams);
            //   this.$router.push({
            //     name: "vein",
            //     params: {
            //       data: {
            //         ...this.faceParams,
            //         isFace: true,
            //         loginType: this.loginType,
            //       },
            //     },
            //   });
            //   storage.local.set("userInfo", {
            //     ...this.faceParams,
            //     isFace: true,
            //     loginType: this.loginType,
            //   });
            // }
    },
    // 处理人脸识别相关的消息
    async handleFaceRecognitionMessages(message) {
      if (!message) return;

      // console.log("处理人脸识别消息:", message.messages);

      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        switch (message.messages.type) {
          case 20002: // 请求人脸识别响应
            if (message.messages.data.code === 1) {
              if (message.messages.data) {
                this.isCameraActive = true;
                this.isProcessing = false;
                this.startCamera();
                // // 根据设备名称匹配对应的摄像头设备
                // const matchedDevice = this.videoDevices.find((device) =>
                //   device.label.includes(message.messages.data.device)
                // );
                // if (matchedDevice) {
                //   // 如果找到匹配的设备，使用该设备的ID启动摄像头
                //   this.startCamera(matchedDevice.deviceId);
                // } else {
                //   // 如果没有找到匹配的设备，使用默认设备
                //   this.speak("未找到匹配的摄像头设备"); // 添加错误语音提示
                // }
              }
            } else {
              this.speak(message.messages.data.msg);
            }

            break;

          case 30002: //人脸识别结果通知
            // 获取人脸识别结果数据
            if (message.messages.data.code === 1) {
              this.speakSync("人脸识别通过");
              this.tipSubMessage = "人脸识别通过";
              this.faceParams = message.messages.data;
              this.isGoHome = false;
              this.endFace();
              this.jumpActionType();
            } else {
              this.tipSubMessage = message.messages.data.msg;
              // 检查距离上次播放是否超过4秒
              const currentTime = Date.now();
              if (currentTime - this.lastFaceResultSpeakTime >= 3000) {
                this.speakSync(message.messages.data.msg);
                this.lastFaceResultSpeakTime = currentTime;
              }
            }

            break;

          case 30102: //人脸图像
            // 获取人脸识别结果数据
            if (message.messages.data.code === 1) {
              this.capturedImage = message.messages.data.img;
            } else {

              if (!this.isPlaying) {
                this.isPlaying = true;
                this.speakSync(message.messages.data.msg);
                setTimeout(() => {
                  this.isPlaying = false;
                }, 2000); // 2秒内不重复播放
              }
              this.tipSubMessage = message.messages.data.msg;
            }

            break;

          // case 20023: //人脸识别结果通知
          //   // 获取人脸识别结果数据
           
          //   break;

        
          default:
            console.log("未处理的消息类型:", message);
        }
      }
    },
  },
  beforeDestroy() {
    this.goBack();
  },
};
</script>
  <style scoped>
.animate-scan {
  animation: scan 2s linear infinite;
}
@keyframes scan {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(500px);
  }
}
/* 自定义输入框样式 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
</style>
  
  