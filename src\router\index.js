import Vue from 'vue'
import VueRouter from 'vue-router'
// import IdentificationView from '../views/IdentificationView.vue'
// import FaceRecognitionPage from '../views/FaceRecognitionPage.vue'
// import Dashboard from '../views/dashboard/index.vue'

import sysSetting from '../views/sysSetting/index.vue'

import Method from '../views/method/index.vue'
import Face from '../views/face/index.vue'
import vein from '../views/vein/index.vue'
import actionType from '../views/actionType/index.vue'

import Save from '../views/save/index.vue'  
import Pick from '../views/pick/index.vue' 
import AuthorizeCode from '../views/authorizecode/index.vue'
import TeamObtain from '../views/teamObtain/index.vue'  
import groupManage from '../views/groupManage/index.vue'
import userManage from '../views/userManage/index.vue'
import scheduling from '../views/scheduling/index.vue'


//采集组件
import collectMethod from '../views/collect/collectMethod/index.vue'
import collectFace from '../views/collect/collectFace/index.vue'
import collectVein from '../views/collect/collectVein/index.vue'

// 测试页面
import InactivityTest from '../views/test/inactivityTest.vue'
import SaveTest from '../views/test/saveTest.vue'

import inactivityMonitor from '@/utils/inactivityMonitor' // 引入活动监控工具

Vue.use(VueRouter)


// 解决重复导航的问题
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => {
    if (err.name !== 'NavigationDuplicated') throw err
  })
}

const routes = [
  {
    path: '/',
    name: 'Method',
    component: Method,
    meta: {
      title: '选择方式',
    }
  },
  {
    path: '/sysSetting',
    name: 'sysSetting',
    component: sysSetting,
    meta: {
      title: '系统设置',
      // hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },
  {
    path: '/Face',
    name: 'Face',
    component: Face,
    meta: {
      title: '人脸识别',
       // hideNetworkStatus: true 
    }
  },
  {
    path: '/vein',
    name: 'vein',
    component: vein,
    meta: {
      title: '静脉识别',
       // hideNetworkStatus: true 
    }
  },
  {
    path: '/actionType',
    name: 'actionType',
    component: actionType,
    meta: {
      title: '操作类型选择',
      // hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },
  {
    path: '/AuthorizeCode',
    name: 'AuthorizeCode',
    component: AuthorizeCode,
    meta: {
      title: '授权码验证',
       hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },
   {
    path: '/TeamObtain',
    name: 'TeamObtain',
    component: TeamObtain,
    meta: {
      title: '班组取证',
       hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },

  {
    path: '/save',
    name: 'save',
    component: Save,
    meta: {
      title: '存证件',
      // hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },
  {
    path: '/pick',
    name: 'pick',
    component: Pick,
    meta: {
      title: '取证件',
      // hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },
  {
    path: '/groupManage',
    name: 'groupManage',
    component: groupManage,
    meta: {
      title: '分组管理',
       hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },
  {
    path: '/userManage',
    name: 'userManage',
    component: userManage,
    meta: {
      title: '用户管理',
      hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },
  {
    path: '/collectMethod',
    name: 'collectMethod',
    component: collectMethod,
    meta: {
      title: '采集方式',
       hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },
  {
    path: '/collectFace',
    name: 'collectFace',
    component: collectFace,
    meta: {
      title: '人脸采集',
       hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },
  {
    path: '/collectVein',
    name: 'collectVein',
    component: collectVein,
    meta: {
      title: '静脉采集',
       hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },
  {
    path: '/scheduling',
    name: 'scheduling',
    component: scheduling,
    meta: {
      title: '调度管理',
       hideNetworkStatus: true // 取消注释可隐藏网络状态栏
    }
  },
  {
    path: '/test/inactivity',
    name: 'InactivityTest',
    component: InactivityTest,
    meta: {
      title: '活动监控测试',
      hideNetworkStatus: true // 隐藏网络状态栏
    }
  },
  {
    path: '/test/save',
    name: 'SaveTest',
    component: SaveTest,
    meta: {
      title: '存证活动监控测试',
      hideNetworkStatus: true // 隐藏网络状态栏
    }
  }
]

const router = new VueRouter({
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || '证件柜'
  
  // 判断是否需要登录权限
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('token')
    if (!token) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }
  
  next()
})

// 路由变化后，重置活动监控计时器
router.afterEach(() => {
  // 路由变化时视为用户活动，重置计时器
  if (inactivityMonitor.isEnabled) {
    inactivityMonitor.resetTimer();
  }
})

export default router 