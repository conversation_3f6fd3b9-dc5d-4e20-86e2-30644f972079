<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div
    class="min-h-screen bg-gradient-to-br from-gray-800 to-gray-900 flex flex-col items-center justify-center relative overflow-hidden tech-grid"
  >
    <Logo class="absolute top-8 left-8 z-40 sm:top-8 left-4 sm:left-8 z-40 transform scale-75 sm:scale-100" />
    <!--使用用户信息栏组件-->
    <UserInfoBar class="absolute bottom-4 sm:bottom-8 right-4 sm:right-8 z-40" />
    <div class="absolute top-4 sm:top-8 right-4 sm:right-8 z-50 flex gap-2 sm:gap-4">
      <!-- 使用HomeButton组件替代原来的返回主页按钮 -->
      <HomeButton @click="onLogoutSuccess"/>

      <!-- 使用LogoutButton组件替代原来的返回登录按钮 -->
      <LogoutButton @confirm="onLogoutSuccess" />
    </div>

    <!-- 现代简约装饰元素 -->
    <div
      class="absolute inset-0 bg-[url('https://ai-public.mastergo.com/ai/img_res/7668f33c5e57ec7fa1f31664082487cb.jpg')] opacity-10 bg-cover"
    ></div>

    <!-- 错误信息 -->
    <!-- 错误提示信息 -->
    <div
      v-if="isErrorMsg"
      class="relative bg-gradient-to-br from-gray-900 to-gray-800 p-4 sm:p-6 md:p-10 rounded-2xl shadow-[0_0_50px_rgba(0,0,0,0.3)] mb-6 sm:mb-12 max-w-4xl mx-auto transform hover:scale-[1.02] transition-all duration-500 border border-white/10 backdrop-blur-lg w-11/12 sm:w-auto"
    >
      <div class="flex flex-col sm:flex-row items-center gap-4 sm:gap-12">
        <!-- 左侧提示图标 -->
        <div class="w-full sm:w-1/4 flex justify-center relative mb-4 sm:mb-0">
          <!-- <div class="absolute -inset-2 bg-red-500/20 blur-lg rounded-full animate-pulse"></div> -->
          <i
            class="fas fa-exclamation-circle text-red-400 text-4xl sm:text-6xl relative animate-float"
          ></i>
        </div>

        <!-- 右侧文字内容 -->
        <div class="w-full sm:w-3/4 sm:pl-8 sm:border-l-2 border-gray-700/50">
          <div class="flex items-center gap-4 mb-4 sm:mb-6 justify-center sm:justify-start">
            <div class="relative">
              <div
                class="absolute -inset-1 bg-red-500/30 blur rounded-lg"
              ></div>
              <!-- <i class="fas fa-exclamation-triangle text-red-400 text-3xl relative"></i> -->
              <h3 class="text-white text-2xl sm:text-3xl font-bold tracking-wide">
                温馨提示
              </h3>
            </div>
          </div>
          <p class="text-gray-200 text-base sm:text-xl leading-relaxed font-medium text-center sm:text-left">
            {{ errorMessage }}
          </p>
        </div>
      </div>
      <!-- 底部装饰线 -->
      <div
        class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 via-red-300 to-red-500"
      ></div>
    </div>

    <!-- 顶部提示文字 -->
    <div
      v-if="!isErrorMsg"
      class="text-white text-sm sm:text-xl font-medium mb-6 sm:mb-12 tracking-wider text-center px-4 sm:px-8 py-3 sm:py-4 rounded-lg bg-white bg-opacity-10 backdrop-blur-md border border-white border-opacity-20 shadow-xl w-11/12 max-w-3xl"
    >
      <div class="flex items-center justify-center space-x-3">
        <i class="fas fa-info-circle text-blue-300 text-lg sm:text-2xl"></i>
        <span>请根据柜格号取出您的证件，取出后请及时关闭柜门</span>
      </div>
    </div>
    <!-- 柜格号显示区域 -->
    <div v-if="!isErrorMsg" class="relative mb-8 sm:mb-16 w-11/12 max-w-3xl px-2 sm:px-4" :key="cabinetNumbersKey">
      <div
        class="bg-white bg-opacity-5 backdrop-blur-sm rounded-2xl p-4 sm:p-6 border border-white border-opacity-10 shadow-2xl"
      >
        <div class="flex justify-center items-center mb-4">
          <h3 class="text-blue-300 text-base sm:text-lg font-medium text-center">
            柜格号分配
          </h3>
          <!-- 滚动控制按钮 -->
          <div
            v-if="showScrollControls"
            class="absolute right-4 sm:right-6 flex space-x-2"
          >
            <button
              @click="scrollCabinet('up')"
              :disabled="!canScrollUp"
              class="p-1.5 sm:p-2 rounded-full bg-gray-700 bg-opacity-50 text-blue-300 hover:bg-opacity-70 transition-colors"
              title="向上滚动"
            >
              <i class="fas fa-chevron-up text-xs"></i>
            </button>
            <button
              @click="scrollCabinet('down')"
              :disabled="!canScrollDown"
              class="p-1.5 sm:p-2 rounded-full bg-gray-700 bg-opacity-50 text-blue-300 hover:bg-opacity-70 transition-colors"
              title="向下滚动"
            >
              <i class="fas fa-chevron-down text-xs"></i>
            </button>
          </div>
        </div>
        <div class="cabinet-container" ref="cabinetContainer">
          <div
            class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 sm:gap-5 md:gap-8"
          >
            <div
              v-for="(num, index) in cabinetNumbers"
              :key="index"
              class="text-center cabinet-item"
            >
              <div class="relative inline-block">
                <div
                  class="absolute inset-0 bg-blue-500 rounded-full opacity-20 blur-md"
                ></div>
                <div
                  class="relative text-2xl sm:text-4xl md:text-5xl font-bold text-blue-300 tracking-wider bg-gray-800 bg-opacity-50 rounded-full w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 flex items-center justify-center mx-auto"
                >
                  {{ num.gridName }}
                </div>
              </div>
              <div
                class="text-gray-300 text-xs sm:text-sm mt-1 sm:mt-2 md:mt-4 uppercase tracking-wider"
              >
                Cabinet {{ num.gridName }}
              </div>
            </div>
          </div>
        </div>
        <!-- 滚动指示器 -->
        <div
          v-if="showScrollControls"
          class="flex justify-center mt-3 sm:mt-4 space-x-1"
        >
          <div
            v-for="i in scrollDots"
            :key="i"
            :class="[
              'w-1 sm:w-1.5 h-1 sm:h-1.5 rounded-full transition-all',
              currentScrollDot === i
                ? 'bg-blue-400 w-2 sm:w-3'
                : 'bg-gray-500 bg-opacity-50',
            ]"
          ></div>
        </div>
      </div>
    </div>

    <!-- 倒计时区域 -->
    <div v-if="!isErrorMsg" class="relative w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48">
      <div
        class="absolute inset-0 rounded-full bg-white bg-opacity-5 backdrop-blur-sm border border-white border-opacity-10 shadow-xl"
      ></div>
      <svg class="w-full h-full" viewBox="0 0 100 100">
        <!-- 背景圆环 -->
        <circle
          cx="50"
          cy="50"
          r="40"
          fill="none"
          stroke="rgba(255,255,255,0.1)"
          stroke-width="4"
        />
        <!-- 进度条圆环 -->
        <circle
          cx="50"
          cy="50"
          r="40"
          fill="none"
          stroke="url(#progressGradient)"
          stroke-width="4"
          stroke-linecap="round"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="dashOffset"
          transform="rotate(-90 50 50)"
        />
        <!-- 渐变定义 -->
        <defs>
          <linearGradient
            id="progressGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="100%"
          >
            <stop offset="0%" stop-color="#3B82F6" />
            <stop offset="100%" stop-color="#10B981" />
          </linearGradient>
        </defs>
      </svg>
      <!-- 倒计时数字 -->
      <div
        class="absolute inset-0 flex flex-col items-center justify-center space-y-1"
      >
        <div class="text-xl sm:text-2xl md:text-3xl font-mono font-bold text-white tracking-wider">
          {{ countdown }}
        </div>
        <div class="text-gray-300 text-xs uppercase tracking-widest">
          Seconds
        </div>
      </div>
    </div>
    <!-- 底部提示 -->
    <div
      v-if="!isErrorMsg"
      class="text-gray-300 text-xs sm:text-sm mt-4 sm:mt-8 flex items-center space-x-2"
    >
      <i class="fas fa-clock text-blue-300"></i>
      <span>倒计时结束后柜门将自动锁定</span>
    </div>
    <!-- 简约粒子背景 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div
        v-for="i in 20"
        :key="'particle-' + i"
        class="absolute rounded-full bg-white opacity-5"
        :style="{
          left: Math.random() * 100 + '%',
          top: Math.random() * 100 + '%',
          width: Math.random() * 4 + 1 + 'px',
          height: Math.random() * 4 + 1 + 'px',
          animation: `float ${Math.random() * 8 + 4}s ease-in-out infinite`,
        }"
      ></div>
    </div>
  </div>
</template>
  <script>
// 导入用户信息栏组件
import UserInfoBar from "@/components/UserInfoBar.vue";
import storage from "@/utils/storage";
import LogoutButton from "@/components/LogoutButton.vue";
import HomeButton from "@/components/HomeButton.vue";
import Logo from "@/components/Logo.vue";
import inactivityMonitor from "@/utils/inactivityMonitor"; // 引入活动监控工具
export default {
  name: "pickCertificate",
  inject: ["speak", "websocketService", "websocketMsg", "speakSync"],
  // 注册组件
  components: {
    UserInfoBar,
    LogoutButton,
    HomeButton,
    Logo,
  },
  data() {
    return {
      cabinetNumbersKey:0,
      routeParams: null,
      cabinetNumbers: [],
      countdown: 90,
      countdownConfig:90,
      timer: null,
      circumference: 2 * Math.PI * 40,
      currentScrollDot: 1, // 当前滚动位置
      isScrolling: false, // 是否正在滚动中
      isErrorMsg: false, //是否显示错误信息
      errorMessage: "", //错误信息
      speakTimer: null, // 用于控制语音播报的定时器
    };
  },

  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息取证消息:", newMessage);
        if (newMessage) {
          this.handlePickMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },

  computed: {
    dashOffset() {
      const progress = this.countdown / this.countdownConfig;
      return this.circumference * (1 - progress);
    },
    // 根据柜格号数量计算滚动点数量
    scrollDots() {
      // 如果柜格号数量少于12个，只显示1个点
      // 每增加8个柜格号，增加一个滚动点，但最多不超过5个点
      const itemCount = this.cabinetNumbers.length;
      if (itemCount <= 12) return 1;
      return Math.min(5, Math.ceil((itemCount - 12) / 8) + 1);
    },
    // 计算是否显示滚动控制和指示器
    showScrollControls() {
      return this.scrollDots > 1;
    },
    // 计算是否可以向上滚动
    canScrollUp() {
      return this.currentScrollDot > 1;
    },
    // 计算是否可以向下滚动
    canScrollDown() {
      return this.currentScrollDot < this.scrollDots;
    },
  },
  created() {
    // 获取路由参数
    this.routeParams = this.$route.params;
    console.log("路由参数22222:", this.routeParams);
    this.isCheckPick();
      // 从本地存储获取倒计时配置值
      const countdownConfig = storage.local.get('countdownConfig');
    // 如果存在配置值则使用，否则保持默认值90
    if (countdownConfig) {
      this.countdown = countdownConfig;
      this.countdownConfig = countdownConfig;
    }
  },
  mounted() {
    //

    // 使用nextTick确保DOM已挂载
    this.$nextTick(() => {
      // 监听柜格容器的滚动事件，更新指示器
      if (this.$refs.cabinetContainer) {
        this.$refs.cabinetContainer.addEventListener(
          "scroll",
          this.handleScroll
        );

        // 检查是否需要显示滚动控制
        this.checkScrollable();

        // 监听窗口大小变化，重新计算滚动点
        window.addEventListener("resize", this.checkScrollable);
      }
    });
  },
  beforeDestroy() {
    this.stopMonitoring();
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
      console.log("倒计时定时器已清除");
    }

    // 确保清除语音播报定时器
    if (this.speakTimer) {
      clearInterval(this.speakTimer);
      this.speakTimer = null;
      console.log("语音播报定时器已清除");
    }

    // 取消所有待播放的语音
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
      console.log("组件销毁时已取消所有待播放的语音");
    }
    // 移除滚动事件监听器
    if (this.$refs.cabinetContainer) {
      this.$refs.cabinetContainer.removeEventListener(
        "scroll",
        this.handleScroll
      );
    }

    // 移除窗口大小变化监听器
    window.removeEventListener("resize", this.checkScrollable);
  },
  methods: {
      stopMonitoring(){
       let order = {
        type: 10400,
        id: "stopMonitoring",
      };
      this.websocketService.send(order);
    },
    onLogoutSuccess() {
      if (this.timer) {
              clearInterval(this.timer);
              this.timer = null;
              console.log("倒计时定时器已清除");
            }

            // 确保清除语音播报定时器
            if (this.speakTimer) {
              clearInterval(this.speakTimer);
              this.speakTimer = null;
              console.log("语音播报定时器已清除");
            }

            // 取消所有待播放的语音
            if (window.speechSynthesis) {
              window.speechSynthesis.cancel();
              console.log("组件销毁时已取消所有待播放的语音");
            }
    },
    //取证消息
    async handlePickMessages(message) {
      if (!message) return;
      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        switch (message.messages.type) {
          case 20008: // 判断现用户是否可以取证
            if (message.messages.data.code === 1) {
              this.startPick();
              this.isErrorMsg = false;
              this.errorMessage = "";
             
            } else {
                  // 判断如果 message.messages.data.authCode 有值，则跳转到授权码页面
                  if (message.messages.data && message.messages.data.authCode) {
                this.$router.push({
                  name: "AuthorizeCode",
                  params: {
                    authCode: message.messages.data.authCode,
                    msg: message.messages.data.msg
                  }
                });
                return;
              }
              
              this.speak(message.messages.data.msg);
              this.isErrorMsg = true;
              this.errorMessage = message.messages.data.msg;
            }
            break;
          case 20009: // 取证响应
            if (message.messages.data.code === 1) {
              this.startCountdown();
              // this.cabinetNumbers = (message?.messages?.data?.list || []).map(item => item.gridCode);

              this.cabinetNumbers = (message?.messages?.data?.list || []).map(item => {
                return {
                  gridCode: item.gridCode,
                  gridName: item.gridName,
                }
              });

              this.cabinetNumbersKey++
              this.speak("请根据柜格号取出您的证件，取出后请及时关闭柜门");
            } else {

               // 判断如果 message.messages.data.authCode 有值，则跳转到授权码页面
               if (message.messages.data && message.messages.data.authCode) {
                this.$router.push({
                  name: "AuthorizeCode",
                  params: {
                    authCode: message.messages.data.authCode,
                    msg: message.messages.data.msg
                  }
                });
                return;
              }

              this.speak(message.messages.data.msg);
              this.isErrorMsg = true;
              this.errorMessage = message.messages.data.msg;
            }
            break;

          case 30006: // 柜门关闭通知
            this.speak(`柜格号${message.messages.data.gridName}柜门已关门`);
            // 从 cabinetNumbers 中移除已关闭的柜格
            this.cabinetNumbers = this.cabinetNumbers.filter(
              (cabinet) => cabinet !== message.messages.data.gridCode
            );
            this.cabinetNumbersKey++

            break;

          case 30007: // 柜门全部关闭通知
            // await this.speakSync("柜门已全部关闭");
            if (this.timer) {
              clearInterval(this.timer);
              this.timer = null;
              console.log("倒计时定时器已清除");
            }

            // 确保清除语音播报定时器
            if (this.speakTimer) {
              clearInterval(this.speakTimer);
              this.speakTimer = null;
              console.log("语音播报定时器已清除");
            }

            // 取消所有待播放的语音
            if (window.speechSynthesis) {
              window.speechSynthesis.cancel();
              console.log("组件销毁时已取消所有待播放的语音");
            }
           
            break;

            case 30009: // 通知取证状态
            if(message.messages.data.code === 1){
              this.speakSync(message?.messages?.data.msg);
            // 延迟3秒后跳转
            setTimeout(() => {
              this.$router.push({
                name: "Method"
              });
            }, 2000);
            }else{
              this.speak(message?.messages?.data.msg);
            }
            break;

            case 30010: // 通知柜门异常
            this.speak(message?.messages?.data.msg);
            // 重置倒计时
            if (this.timer) {
              clearInterval(this.timer);
              this.timer = null;
              console.log("倒计时定时器已清除");
              const countdownConfig = storage.local.get('countdownConfig');
              if (countdownConfig) {
                this.countdown = countdownConfig;
                this.countdownConfig = countdownConfig;
              }else{
                this.countdown = 90;
                this.countdownConfig = 90;
              }
            }
            this.startCountdown(); // 重新开始倒计时
          
            break;

          default:
          // console.log("未处理的消息类型:", message);
        }
      }
    },

    async isCheckPick() {
      // console.log("isCheckPick",routeParams)
      //查询用户是否可以取证
      const userInfo = await storage.local.get("userInfo");
      let order = {
        type: 10008,
        id: "isCheckPick",
        data: {
          account: userInfo.account,
          username: userInfo.account,
          authCode:this.$route.params.authCode,
          code:this.$route.params.code,
        },
      };
      this.websocketService.send(order);
    },

    async startPick() {
      // 取证
      const userInfo = await storage.local.get("userInfo");
      let order = {
        type: 10009,
        id: "startPick",
        data: {
          account: userInfo.account,
          username: userInfo.account,
          authCode:this.$route.params.authCode,
          code:this.$route.params.code,
        },
      };
      this.websocketService.send(order);
    },

    // 返回首页
    goHome() {
      this.stopInfiniteSpeak();
      this.$router.push({
        name: "actionType",
        params: { data: { ...this.routeParams } },
      });
    },

    startCountdown() {
      // 停止活动监控
      inactivityMonitor.stop();
      console.log("倒计时开始，已停止活动监控");

      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(this.timer);
          // 倒计时结束后的处理逻辑
          this.handleCountdownEnd();
        }
      }, 1000);
    },

    // 倒计时结束处理
    handleCountdownEnd() {
      // 这里可以添加倒计时结束后的操作，比如自动返回首页、显示提示等
      // this.$router.push("/");
      // this.startInfiniteSpeak();
      // 防止重复触发，先检查timer是否已经被清除
      if (!this.timer) {
        console.log("倒计时已经结束，避免重复触发");
        return;
      }
      // 清除倒计时定时器
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        console.log("倒计时定时器已清除");
      }

      // 设置状态标记，表示倒计时已结束
      this.countdown = 0;

      // 重新启动活动监控
      this.restartInactivityMonitor();

      // 只有在没有启动语音播报的情况下才启动
      if (!this.speakTimer) {
        console.log("开始播报关闭柜门提示");
        this.startInfiniteSpeak("请关闭柜门", 3000);
      } else {
        this.stopInfiniteSpeak();
        console.log("语音播报已在运行中，避免重复启动");
      }
    },

    // 检查是否需要滚动
    checkScrollable() {
      const container = this.$refs.cabinetContainer;
      if (!container) return;

      // 如果内容高度小于容器高度，则不需要滚动，强制设置滚动点为1
      if (container.scrollHeight <= container.clientHeight) {
        this.currentScrollDot = 1;
      }
    },

    // 处理滚动事件
    handleScroll() {
      if (this.isScrolling) return; // 如果正在程序控制滚动，不处理用户滚动事件

      const container = this.$refs.cabinetContainer;
      const scrollPercentage =
        container.scrollTop / (container.scrollHeight - container.clientHeight);

      // 根据滚动百分比计算当前点
      this.currentScrollDot = Math.min(
        this.scrollDots,
        Math.max(1, Math.ceil(scrollPercentage * this.scrollDots))
      );
    },

    // 控制柜格号容器滚动
    scrollCabinet(direction) {
      if (!this.$refs.cabinetContainer) return;

      const container = this.$refs.cabinetContainer;

      // 确保滚动前先暂停滚动事件处理
      this.isScrolling = true;

      // 根据方向计算滚动位置
      let targetDot =
        direction === "up"
          ? Math.max(1, this.currentScrollDot - 1)
          : Math.min(this.scrollDots, this.currentScrollDot + 1);

      // 计算需要滚动到的位置
      const scrollHeight = container.scrollHeight - container.clientHeight;
      const targetScroll =
        scrollHeight * ((targetDot - 1) / (this.scrollDots - 1 || 1));

      // 设置滚动位置
      container.scrollTo({
        top: targetScroll,
        behavior: "smooth",
      });

      // 更新当前滚动点
      this.currentScrollDot = targetDot;

      // 滚动完成后恢复滚动事件处理
      setTimeout(() => {
        this.isScrolling = false;
      }, 500);
    },

    // 更新滚动指示器
    updateScrollDot(change) {
      let newDot = this.currentScrollDot + change;
      if (newDot < 1) newDot = 1;
      if (newDot > this.scrollDots) newDot = this.scrollDots;
      this.currentScrollDot = newDot;
    },

    /**
     * 开始无限循环播报
     * @param {string} message - 要播报的消息
     * @param {number} interval - 播报间隔时间(毫秒)，默认3000ms
     */
    startInfiniteSpeak(message = "请关闭柜门", interval = 3000) {
      // 先清除可能存在的定时器
      this.stopInfiniteSpeak();

      // 立即播报一次
      this.speak(message);

      // 设置定时器，定期播报
      this.speakTimer = setInterval(() => {
        this.speak(message);
      }, interval);
    },

    /**
     * 停止无限循环播报
     */
    stopInfiniteSpeak() {
      if (this.speakTimer) {
        clearInterval(this.speakTimer);
        this.speakTimer = null;
      }
    },

    /**
     * 重新启动活动监控
     */
    restartInactivityMonitor() {
      // 从父组件获取活动监控的配置
      const app = this.$parent;
      if (app && app.times && app.startInactivityMonitor) {
        console.log("倒计时结束，重新启动活动监控");
        app.startInactivityMonitor();
      } else {
        console.log("无法获取活动监控配置，尝试直接启动");
        // 如果无法从父组件获取配置，使用默认配置
        inactivityMonitor.start(
          () => {
            console.log("自动跳转到首页");
            this.$router.push("/");
          },
          300000, // 默认5分钟
          (remainingSeconds) => {
            console.log(remainingSeconds);
          },
          30000 // 警告提前时间30秒
        );
      }
    },
  },
};
</script>
  <style scoped>
@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-10px) translateX(5px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 柜格号容器滚动样式 */
.cabinet-container {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 8px;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(255, 255, 255, 0.1);
  scroll-behavior: smooth; /* 添加平滑滚动效果 */
}

/* 响应式调整 */
@media (min-width: 640px) {
  .cabinet-container {
    max-height: 250px;
  }
}

@media (min-width: 768px) {
  .cabinet-container {
    max-height: 300px;
  }
}

@media (min-width: 1024px) {
  .cabinet-container {
    max-height: 350px;
  }
}

/* 在触摸屏设备上优化滚动体验 */
@media (hover: none) {
  .cabinet-container {
    -webkit-overflow-scrolling: touch; /* 在iOS设备上实现平滑滚动 */
    padding-right: 0; /* 触摸设备上移除右侧padding */
  }
}

/* 自定义滚动条样式 */
.cabinet-container::-webkit-scrollbar {
  width: 4px;
}

@media (min-width: 640px) {
  .cabinet-container::-webkit-scrollbar {
    width: 6px;
  }
}

.cabinet-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.cabinet-container::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 10px;
}

.cabinet-container::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* 柜格项悬停效果 */
.cabinet-item {
  transition: transform 0.2s ease-in-out, opacity 0.2s ease;
}

.cabinet-item:hover {
  transform: translateY(-3px);
  opacity: 0.9;
}

/* 触摸设备上禁用悬停效果 */
@media (hover: none) {
  .cabinet-item:hover {
    transform: none;
    opacity: 1;
  }
}
.user-info-bar {
  background: rgba(28, 31, 55, 0.7);
  border-radius: 4px;
  padding: 8px 12px;
}

/* 适配小屏幕设备 */
@media (max-height: 600px) {
  .cabinet-container {
    max-height: 150px;
  }
}

/* 适配超小屏幕设备 */
@media (max-width: 360px) {
  .cabinet-container {
    padding-right: 4px;
  }
  
  .cabinet-container::-webkit-scrollbar {
    width: 3px;
  }
}
</style>
  